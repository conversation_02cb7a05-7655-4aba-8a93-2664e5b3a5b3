import bcrypt from 'bcryptjs'
import type { PoolConnection } from 'mysql2/promise'
import { BaseRepository } from './base'
import { safeExecuteQuery, safeExecuteQuerySingle, safeExecuteTransaction } from '../utils/database'

// 用户数据模型
export interface User {
  user_id: number
  username: string
  password_hash: string
  email: string
  full_name: string | null
  role: 'teacher' | 'student'
  created_at: Date
}

// 创建用户数据
export interface CreateUserData {
  username: string
  password: string
  email: string
  full_name?: string
  role: 'teacher' | 'student'
}

// 更新用户数据
export interface UpdateUserData {
  username?: string
  password?: string
  email?: string
  full_name?: string
}

// 用户查询选项
export interface UserQueryOptions {
  role?: 'teacher' | 'student'
  username?: string
  email?: string
}

// 用户仓储类
export class UserRepository extends BaseRepository<User> {
  protected tableName = 'users'
  protected primaryKey = 'user_id'

  // 根据用户名查找用户
  async findByUsername(username: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE username = ? LIMIT 1'
    return safeExecuteQuerySingle<User>(sql, [username])
  }

  // 根据邮箱查找用户
  async findByEmail(email: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE email = ? LIMIT 1'
    return safeExecuteQuerySingle<User>(sql, [email])
  }

  // 根据角色查找用户
  async findByRole(role: 'teacher' | 'student'): Promise<User[]> {
    const sql = 'SELECT * FROM users WHERE role = ? ORDER BY full_name, username'
    return safeExecuteQuery<User>(sql, [role])
  }

  // 创建用户（带密码哈希）
  async createUser(userData: CreateUserData): Promise<User> {
    // 检查用户名是否已存在
    const existingUser = await this.findByUsername(userData.username)
    if (existingUser) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否已存在
    const existingEmail = await this.findByEmail(userData.email)
    if (existingEmail) {
      throw new Error('邮箱已存在')
    }

    // 哈希密码
    const passwordHash = await this.hashPassword(userData.password)

    // 创建用户数据
    const userToCreate = {
      username: userData.username,
      password_hash: passwordHash,
      email: userData.email,
      full_name: userData.full_name || null,
      role: userData.role
    }

    return this.create(userToCreate)
  }

  // 更新用户信息
  async updateUser(userId: number, userData: UpdateUserData): Promise<User | null> {
    const updateData: Partial<User> = {}

    // 如果更新用户名，检查是否已存在
    if (userData.username) {
      const existingUser = await this.findByUsername(userData.username)
      if (existingUser && existingUser.user_id !== userId) {
        throw new Error('用户名已存在')
      }
      updateData.username = userData.username
    }

    // 如果更新邮箱，检查是否已存在
    if (userData.email) {
      const existingEmail = await this.findByEmail(userData.email)
      if (existingEmail && existingEmail.user_id !== userId) {
        throw new Error('邮箱已存在')
      }
      updateData.email = userData.email
    }

    // 如果更新密码，进行哈希
    if (userData.password) {
      updateData.password_hash = await this.hashPassword(userData.password)
    }

    // 更新其他字段
    if (userData.full_name !== undefined) {
      updateData.full_name = userData.full_name
    }

    return this.update(userId, updateData)
  }

  // 验证用户密码
  async verifyPassword(username: string, password: string): Promise<User | null> {
    const user = await this.findByUsername(username)
    if (!user) {
      return null
    }

    const isValid = await bcrypt.compare(password, user.password_hash)
    if (!isValid) {
      return null
    }

    return user
  }

  // 更改用户密码
  async changePassword(userId: number, oldPassword: string, newPassword: string): Promise<boolean> {
    const user = await this.findById(userId)
    if (!user) {
      throw new Error('用户不存在')
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password_hash)
    if (!isOldPasswordValid) {
      throw new Error('旧密码不正确')
    }

    // 哈希新密码
    const newPasswordHash = await this.hashPassword(newPassword)

    // 更新密码
    const result = await this.update(userId, { password_hash: newPasswordHash })
    return result !== null
  }

  // 获取用户统计信息
  async getUserStats(): Promise<{ teachers: number; students: number; total: number }> {
    const sql = `
      SELECT 
        role,
        COUNT(*) as count
      FROM users 
      GROUP BY role
    `
    
    const results = await safeExecuteQuery<{ role: string; count: number }>(sql)
    
    let teachers = 0
    let students = 0
    
    results.forEach(result => {
      if (result.role === 'teacher') {
        teachers = result.count
      } else if (result.role === 'student') {
        students = result.count
      }
    })
    
    return {
      teachers,
      students,
      total: teachers + students
    }
  }

  // 搜索用户
  async searchUsers(query: string, role?: 'teacher' | 'student'): Promise<User[]> {
    let sql = `
      SELECT * FROM users 
      WHERE (username LIKE ? OR full_name LIKE ? OR email LIKE ?)
    `
    const params = [`%${query}%`, `%${query}%`, `%${query}%`]

    if (role) {
      sql += ' AND role = ?'
      params.push(role)
    }

    sql += ' ORDER BY full_name, username LIMIT 50'

    return safeExecuteQuery<User>(sql, params)
  }

  // 批量创建用户（事务）
  async createUsersInBatch(usersData: CreateUserData[]): Promise<User[]> {
    return safeExecuteTransaction(async (connection) => {
      const createdUsers: User[] = []

      for (const userData of usersData) {
        // 检查用户名是否已存在
        const existingUser = await this.findByUsernameInTransaction(connection, userData.username)
        if (existingUser) {
          throw new Error(`用户名 ${userData.username} 已存在`)
        }

        // 检查邮箱是否已存在
        const existingEmail = await this.findByEmailInTransaction(connection, userData.email)
        if (existingEmail) {
          throw new Error(`邮箱 ${userData.email} 已存在`)
        }

        // 哈希密码
        const passwordHash = await this.hashPassword(userData.password)

        // 创建用户数据
        const userToCreate = {
          username: userData.username,
          password_hash: passwordHash,
          email: userData.email,
          full_name: userData.full_name || null,
          role: userData.role
        }

        const createdUser = await this.createInTransaction(connection, userToCreate)
        createdUsers.push(createdUser)
      }

      return createdUsers
    })
  }

  // 私有方法：哈希密码
  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
  }

  // 私有方法：在事务中根据用户名查找用户
  private async findByUsernameInTransaction(connection: PoolConnection, username: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE username = ? LIMIT 1'
    const results = await connection.execute(sql, [username])
    const rows = results[0] as User[]
    return rows.length > 0 ? (rows[0] || null) : null
  }

  // 私有方法：在事务中根据邮箱查找用户
  private async findByEmailInTransaction(connection: PoolConnection, email: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE email = ? LIMIT 1'
    const results = await connection.execute(sql, [email])
    const rows = results[0] as User[]
    return rows.length > 0 ? (rows[0] || null) : null
  }

  // 获取不包含密码的用户信息
  async findByIdSafe(id: number): Promise<Omit<User, 'password_hash'> | null> {
    const sql = `
      SELECT user_id, username, email, full_name, role, created_at 
      FROM users 
      WHERE user_id = ? 
      LIMIT 1
    `
    return safeExecuteQuerySingle<Omit<User, 'password_hash'>>(sql, [id])
  }

  // 获取所有用户（不包含密码）
  async findAllSafe(role?: 'teacher' | 'student'): Promise<Omit<User, 'password_hash'>[]> {
    let sql = `
      SELECT user_id, username, email, full_name, role, created_at 
      FROM users
    `
    const params: any[] = []

    if (role) {
      sql += ' WHERE role = ?'
      params.push(role)
    }

    sql += ' ORDER BY full_name, username'

    return safeExecuteQuery<Omit<User, 'password_hash'>>(sql, params)
  }
}