import type { PoolConnection } from 'mysql2/promise'
import { executeQueryInTransaction, executeQuerySingleInTransaction } from '../config/database'
import { safeExecuteQuery, safeExecuteQuerySingle } from '../utils/database'

// 基础仓储接口
export interface IBaseRepository<T, ID = number> {
  findById(id: ID): Promise<T | null>
  findAll(options?: QueryOptions): Promise<T[]>
  create(data: Partial<T>): Promise<T>
  update(id: ID, data: Partial<T>): Promise<T | null>
  delete(id: ID): Promise<boolean>
  count(where?: Record<string, any>): Promise<number>
}

// 查询选项接口
export interface QueryOptions {
  where?: Record<string, any>
  orderBy?: string
  limit?: number
  offset?: number
}

// 基础仓储抽象类
export abstract class BaseRepository<T, ID = number> implements IBaseRepository<T, ID> {
  protected abstract tableName: string
  protected abstract primaryKey: string

  // 根据ID查找记录
  async findById(id: ID): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ? LIMIT 1`
    return safeExecuteQuerySingle<T>(sql, [id])
  }

  // 查找所有记录
  async findAll(options: QueryOptions = {}): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName}`
    const params: any[] = []

    // 添加WHERE条件
    if (options.where && Object.keys(options.where).length > 0) {
      const conditions = Object.keys(options.where).map(key => {
        params.push(options.where![key])
        return `${key} = ?`
      })
      sql += ` WHERE ${conditions.join(' AND ')}`
    }

    // 添加ORDER BY
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`
    }

    // 添加LIMIT和OFFSET
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`
      if (options.offset) {
        sql += ` OFFSET ${options.offset}`
      }
    }

    return safeExecuteQuery<T>(sql, params)
  }

  // 创建记录
  async create(data: Partial<T>): Promise<T> {
    const fields = Object.keys(data).filter(key => data[key as keyof T] !== undefined)
    const values = fields.map(key => data[key as keyof T])
    const placeholders = fields.map(() => '?').join(', ')

    const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`
    const result = await safeExecuteQuery(sql, values) as any

    const createdRecord = await this.findById(result.insertId as ID)
    if (!createdRecord) {
      throw new Error(`Failed to retrieve created record with ID: ${result.insertId}`)
    }

    return createdRecord
  }

  // 更新记录
  async update(id: ID, data: Partial<T>): Promise<T | null> {
    const fields = Object.keys(data).filter(key => data[key as keyof T] !== undefined)
    if (fields.length === 0) {
      return this.findById(id)
    }

    const setClause = fields.map(key => `${key} = ?`).join(', ')
    const values = fields.map(key => data[key as keyof T])

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`
    const params = [...values, id]

    const result = await safeExecuteQuery(sql, params) as any
    
    if (result.affectedRows === 0) {
      return null
    }

    return this.findById(id)
  }

  // 删除记录
  async delete(id: ID): Promise<boolean> {
    const sql = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`
    const result = await safeExecuteQuery(sql, [id]) as any
    return result.affectedRows > 0
  }

  // 统计记录数
  async count(where: Record<string, any> = {}): Promise<number> {
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`
    const params: any[] = []

    if (Object.keys(where).length > 0) {
      const conditions = Object.keys(where).map(key => {
        params.push(where[key])
        return `${key} = ?`
      })
      sql += ` WHERE ${conditions.join(' AND ')}`
    }

    const result = await safeExecuteQuerySingle<{ count: number }>(sql, params)
    return result?.count || 0
  }

  // 在事务中执行操作
  protected async executeInTransaction<R>(
    connection: PoolConnection,
    callback: (connection: PoolConnection) => Promise<R>
  ): Promise<R> {
    return callback(connection)
  }

  // 在事务中根据ID查找记录
  protected async findByIdInTransaction(connection: PoolConnection, id: ID): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ? LIMIT 1`
    return executeQuerySingleInTransaction<T>(connection, sql, [id])
  }

  // 在事务中创建记录
  protected async createInTransaction(connection: PoolConnection, data: Partial<T>): Promise<T> {
    const fields = Object.keys(data).filter(key => data[key as keyof T] !== undefined)
    const values = fields.map(key => data[key as keyof T])
    const placeholders = fields.map(() => '?').join(', ')

    const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`
    const result = await executeQueryInTransaction(connection, sql, values) as any

    const createdRecord = await this.findByIdInTransaction(connection, result.insertId as ID)
    if (!createdRecord) {
      throw new Error(`Failed to retrieve created record with ID: ${result.insertId}`)
    }

    return createdRecord
  }

  // 在事务中更新记录
  protected async updateInTransaction(connection: PoolConnection, id: ID, data: Partial<T>): Promise<T | null> {
    const fields = Object.keys(data).filter(key => data[key as keyof T] !== undefined)
    if (fields.length === 0) {
      return this.findByIdInTransaction(connection, id)
    }

    const setClause = fields.map(key => `${key} = ?`).join(', ')
    const values = fields.map(key => data[key as keyof T])

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`
    const params = [...values, id]

    const result = await executeQueryInTransaction(connection, sql, params) as any
    
    if (result.affectedRows === 0) {
      return null
    }

    return this.findByIdInTransaction(connection, id)
  }

  // 在事务中删除记录
  protected async deleteInTransaction(connection: PoolConnection, id: ID): Promise<boolean> {
    const sql = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`
    const result = await executeQueryInTransaction(connection, sql, [id]) as any
    return result.affectedRows > 0
  }
}