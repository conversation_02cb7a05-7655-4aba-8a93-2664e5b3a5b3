import { describe, it, expect, beforeEach } from 'vitest'
import { FeedbackRepository, type CreateFeedbackData, type UpdateFeedbackData } from './feedback'

describe('FeedbackRepository', () => {
  let repository: FeedbackRepository

  beforeEach(() => {
    repository = new FeedbackRepository()
  })

  describe('repository structure', () => {
    it('should have correct table name and primary key', () => {
      expect(repository['tableName']).toBe('feedback')
      expect(repository['primaryKey']).toBe('feedback_id')
    })
  })

  describe('data validation', () => {
    it('should validate feedback creation data structure', () => {
      const feedbackData: CreateFeedbackData = {
        submission_id: 1,
        teacher_id: 1,
        comment: 'Great work! The implementation is correct and well-documented.'
      }

      expect(feedbackData.submission_id).toBeDefined()
      expect(feedbackData.teacher_id).toBeDefined()
      expect(feedbackData.comment).toBeDefined()
      expect(typeof feedbackData.submission_id).toBe('number')
      expect(typeof feedbackData.teacher_id).toBe('number')
      expect(typeof feedbackData.comment).toBe('string')
    })

    it('should validate feedback update data structure', () => {
      const updateData: UpdateFeedbackData = {
        comment: 'Updated feedback comment'
      }

      expect(updateData.comment).toBeDefined()
      expect(typeof updateData.comment).toBe('string')
    })

    it('should allow empty update data', () => {
      const updateData: UpdateFeedbackData = {}

      expect(updateData.comment).toBeUndefined()
    })
  })

  describe('method signatures', () => {
    it('should have all required methods', () => {
      expect(typeof repository.createFeedback).toBe('function')
      expect(typeof repository.createFeedbackWithStatus).toBe('function')
      expect(typeof repository.updateFeedback).toBe('function')
      expect(typeof repository.findBySubmissionId).toBe('function')
      expect(typeof repository.findByStudentId).toBe('function')
      expect(typeof repository.findByTeacherId).toBe('function')
      expect(typeof repository.findRecentFeedback).toBe('function')
      expect(typeof repository.findRecentByStudentId).toBe('function')
      expect(typeof repository.findRecentByTeacherId).toBe('function')
      expect(typeof repository.searchFeedback).toBe('function')
      expect(typeof repository.getFeedbackStats).toBe('function')
      expect(typeof repository.getFeedbackStatsByTaskId).toBe('function')
      expect(typeof repository.hasSubmissionFeedback).toBe('function')
      expect(typeof repository.findByIdWithDetails).toBe('function')
      expect(typeof repository.deleteFeedback).toBe('function')
    })
  })

  describe('interface compliance', () => {
    it('should comply with Feedback interface', () => {
      const mockFeedback = {
        feedback_id: 1,
        submission_id: 1,
        teacher_id: 1,
        comment: 'Test feedback comment',
        created_at: new Date()
      }

      expect(mockFeedback.feedback_id).toBeDefined()
      expect(mockFeedback.submission_id).toBeDefined()
      expect(mockFeedback.teacher_id).toBeDefined()
      expect(mockFeedback.comment).toBeDefined()
      expect(mockFeedback.created_at).toBeInstanceOf(Date)
    })

    it('should comply with FeedbackWithDetails interface', () => {
      const mockFeedbackWithDetails = {
        feedback_id: 1,
        submission_id: 1,
        teacher_id: 1,
        comment: 'Test feedback comment',
        created_at: new Date(),
        task_title: 'Test Task',
        project_name: 'Test Project',
        student_name: 'John Doe',
        student_username: 'johndoe',
        teacher_name: 'Jane Smith',
        submission_status: 'completed',
        submission_date: new Date(),
        progress_description: 'Completed the assignment'
      }

      expect(mockFeedbackWithDetails.task_title).toBeDefined()
      expect(mockFeedbackWithDetails.project_name).toBeDefined()
      expect(mockFeedbackWithDetails.student_name).toBeDefined()
      expect(mockFeedbackWithDetails.student_username).toBeDefined()
      expect(mockFeedbackWithDetails.teacher_name).toBeDefined()
      expect(mockFeedbackWithDetails.submission_status).toBeDefined()
      expect(mockFeedbackWithDetails.submission_date).toBeInstanceOf(Date)
      expect(mockFeedbackWithDetails.progress_description).toBeDefined()
    })

    it('should comply with FeedbackStats interface', () => {
      const mockFeedbackStats = {
        total_feedback: 100,
        recent_feedback: 15,
        feedback_by_teacher: [
          {
            teacher_id: 1,
            teacher_name: 'Jane Smith',
            feedback_count: 25
          },
          {
            teacher_id: 2,
            teacher_name: 'John Doe',
            feedback_count: 20
          }
        ]
      }

      expect(typeof mockFeedbackStats.total_feedback).toBe('number')
      expect(typeof mockFeedbackStats.recent_feedback).toBe('number')
      expect(Array.isArray(mockFeedbackStats.feedback_by_teacher)).toBe(true)
      
      mockFeedbackStats.feedback_by_teacher.forEach(teacher => {
        expect(teacher.teacher_id).toBeDefined()
        expect(teacher.teacher_name).toBeDefined()
        expect(typeof teacher.feedback_count).toBe('number')
      })
    })
  })

  describe('status validation', () => {
    it('should validate submission status values for feedback creation', () => {
      const validStatuses = ['completed', 'needs_revision']
      
      validStatuses.forEach(status => {
        expect(['completed', 'needs_revision']).toContain(status)
      })
    })

    it('should handle feedback statistics by task', () => {
      const mockTaskStats = {
        total_feedback: 10,
        positive_feedback: 7,
        revision_feedback: 3
      }

      expect(typeof mockTaskStats.total_feedback).toBe('number')
      expect(typeof mockTaskStats.positive_feedback).toBe('number')
      expect(typeof mockTaskStats.revision_feedback).toBe('number')
      
      // Positive + revision should not exceed total
      expect(mockTaskStats.positive_feedback + mockTaskStats.revision_feedback)
        .toBeLessThanOrEqual(mockTaskStats.total_feedback)
    })
  })

  describe('query parameter validation', () => {
    it('should validate search parameters', () => {
      const query = 'excellent work'
      const teacherId = 1
      const studentId = 2

      expect(typeof query).toBe('string')
      expect(typeof teacherId).toBe('number')
      expect(typeof studentId).toBe('number')
    })

    it('should validate limit parameters', () => {
      const limit = 10

      expect(typeof limit).toBe('number')
      expect(limit).toBeGreaterThan(0)
    })
  })

  describe('data transformation', () => {
    it('should handle feedback creation data transformation', () => {
      const feedbackData: CreateFeedbackData = {
        submission_id: 1,
        teacher_id: 1,
        comment: 'Test feedback'
      }

      const expectedData = {
        submission_id: feedbackData.submission_id,
        teacher_id: feedbackData.teacher_id,
        comment: feedbackData.comment
      }

      expect(expectedData.submission_id).toBe(1)
      expect(expectedData.teacher_id).toBe(1)
      expect(expectedData.comment).toBe('Test feedback')
    })

    it('should handle optional update fields', () => {
      const updateData: UpdateFeedbackData = {
        comment: 'Updated comment'
      }

      const expectedUpdateData: Partial<any> = {}

      if (updateData.comment !== undefined) {
        expectedUpdateData.comment = updateData.comment
      }

      expect(expectedUpdateData.comment).toBe('Updated comment')
    })
  })

  describe('statistics validation', () => {
    it('should validate feedback statistics structure', () => {
      const mockStats = {
        total_feedback: 0,
        recent_feedback: 0,
        feedback_by_teacher: []
      }

      expect(mockStats.total_feedback).toBeGreaterThanOrEqual(0)
      expect(mockStats.recent_feedback).toBeGreaterThanOrEqual(0)
      expect(mockStats.recent_feedback).toBeLessThanOrEqual(mockStats.total_feedback)
      expect(Array.isArray(mockStats.feedback_by_teacher)).toBe(true)
    })

    it('should validate task feedback statistics', () => {
      const mockTaskStats = {
        total_feedback: 0,
        positive_feedback: 0,
        revision_feedback: 0
      }

      Object.values(mockTaskStats).forEach(count => {
        expect(count).toBeGreaterThanOrEqual(0)
      })

      expect(mockTaskStats.positive_feedback + mockTaskStats.revision_feedback)
        .toBeLessThanOrEqual(mockTaskStats.total_feedback)
    })
  })

  describe('comment validation', () => {
    it('should handle various comment lengths', () => {
      const shortComment = 'Good'
      const longComment = 'This is a very detailed feedback comment that provides comprehensive information about the student\'s work and suggestions for improvement.'

      expect(typeof shortComment).toBe('string')
      expect(typeof longComment).toBe('string')
      expect(shortComment.length).toBeGreaterThan(0)
      expect(longComment.length).toBeGreaterThan(0)
    })

    it('should handle special characters in comments', () => {
      const commentWithSpecialChars = 'Great work! 👍 Your code is 100% correct. Keep it up! 🎉'

      expect(typeof commentWithSpecialChars).toBe('string')
      expect(commentWithSpecialChars.length).toBeGreaterThan(0)
    })
  })
})
