import { describe, it, expect, beforeEach } from 'vitest'
import { UserRepository, type CreateUserData, type UpdateUserData } from './user'
import bcrypt from 'bcryptjs'

describe('UserRepository', () => {
  let repository: UserRepository

  beforeEach(() => {
    repository = new UserRepository()
  })

  describe('password hashing', () => {
    it('should hash passwords correctly', async () => {
      const password = 'testpassword123'
      const hashedPassword = await bcrypt.hash(password, 12)

      expect(hashedPassword).toBeDefined()
      expect(hashedPassword).not.toBe(password)
      expect(hashedPassword.length).toBeGreaterThan(50)
    })

    it('should verify passwords correctly', async () => {
      const password = 'testpassword123'
      const hashedPassword = await bcrypt.hash(password, 12)

      const isValid = await bcrypt.compare(password, hashedPassword)
      const isInvalid = await bcrypt.compare('wrongpassword', hashedPassword)

      expect(isValid).toBe(true)
      expect(isInvalid).toBe(false)
    })
  })

  describe('repository structure', () => {
    it('should have correct table name and primary key', () => {
      expect(repository['tableName']).toBe('users')
      expect(repository['primaryKey']).toBe('user_id')
    })
  })

  describe('data validation', () => {
    it('should validate user data structure', () => {
      const userData: CreateUserData = {
        username: 'testuser',
        password: 'testpassword',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'student'
      }

      expect(userData.username).toBeDefined()
      expect(userData.password).toBeDefined()
      expect(userData.email).toBeDefined()
      expect(userData.role).toMatch(/^(teacher|student)$/)
    })

    it('should validate update data structure', () => {
      const updateData: UpdateUserData = {
        username: 'newusername',
        email: '<EMAIL>',
        full_name: 'New Name'
      }

      expect(updateData.username).toBeDefined()
      expect(updateData.email).toBeDefined()
      expect(updateData.full_name).toBeDefined()
    })
  })
})

