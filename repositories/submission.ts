import { BaseRepository } from './base'
import { safeExecuteQuery, safeExecuteQuerySingle, safeExecuteTransaction } from '../utils/database'


// 提交数据模型
export interface Submission {
  submission_id: number
  task_id: number
  student_id: number
  progress_description: string
  git_commit_hash: string | null
  submission_date: Date
  status: 'in_progress' | 'submitted' | 'completed' | 'needs_revision'
}

// 创建提交数据
export interface CreateSubmissionData {
  task_id: number
  student_id: number
  progress_description: string
  git_commit_hash?: string
  status?: 'in_progress' | 'submitted'
}

// 更新提交数据
export interface UpdateSubmissionData {
  progress_description?: string
  git_commit_hash?: string
  status?: 'in_progress' | 'submitted' | 'completed' | 'needs_revision'
}

// 提交详情（包含任务和学生信息）
export interface SubmissionWithDetails extends Submission {
  task_title: string
  project_id: number
  project_name: string
  student_name: string
  student_username: string
  teacher_id: number
  git_repo_url: string | null
}

// 提交统计信息
export interface SubmissionStats {
  total_submissions: number
  pending_review: number
  completed: number
  needs_revision: number
  in_progress: number
}

// 提交仓储类
export class SubmissionRepository extends BaseRepository<Submission> {
  protected tableName = 'submissions'
  protected primaryKey = 'submission_id'

  // 创建提交
  async createSubmission(submissionData: CreateSubmissionData): Promise<Submission> {
    const dataToCreate = {
      task_id: submissionData.task_id,
      student_id: submissionData.student_id,
      progress_description: submissionData.progress_description,
      git_commit_hash: submissionData.git_commit_hash || null,
      status: submissionData.status || 'submitted'
    }

    return this.create(dataToCreate)
  }

  // 更新提交
  async updateSubmission(submissionId: number, submissionData: UpdateSubmissionData): Promise<Submission | null> {
    const updateData: Partial<Submission> = {}

    if (submissionData.progress_description !== undefined) {
      updateData.progress_description = submissionData.progress_description
    }

    if (submissionData.git_commit_hash !== undefined) {
      updateData.git_commit_hash = submissionData.git_commit_hash
    }

    if (submissionData.status !== undefined) {
      updateData.status = submissionData.status
    }

    return this.update(submissionId, updateData)
  }

  // 更新提交状态
  async updateSubmissionStatus(submissionId: number, status: Submission['status']): Promise<boolean> {
    const result = await this.update(submissionId, { status })
    return result !== null
  }

  // 根据任务ID查找提交
  async findByTaskId(taskId: number): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE s.task_id = ?
      ORDER BY s.submission_date DESC
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [taskId])
  }

  // 根据学生ID查找提交
  async findByStudentId(studentId: number): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE s.student_id = ?
      ORDER BY s.submission_date DESC
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [studentId])
  }

  // 根据教师ID查找需要审阅的提交
  async findPendingReviewByTeacherId(teacherId: number): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT
        s.*,
        t.title as task_title,
        p.project_id,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE p.teacher_id = ? AND s.status = 'submitted'
      ORDER BY s.submission_date ASC
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [Number(teacherId)])
  }

  // 获取学生最新的提交
  async findLatestByTaskAndStudent(taskId: number, studentId: number): Promise<Submission | null> {
    const sql = `
      SELECT * FROM submissions 
      WHERE task_id = ? AND student_id = ? 
      ORDER BY submission_date DESC 
      LIMIT 1
    `
    return safeExecuteQuerySingle<Submission>(sql, [taskId, studentId])
  }

  // 获取学生在任务中的所有提交历史
  async findHistoryByTaskAndStudent(taskId: number, studentId: number): Promise<Submission[]> {
    const sql = `
      SELECT * FROM submissions 
      WHERE task_id = ? AND student_id = ? 
      ORDER BY submission_date DESC
    `
    return safeExecuteQuery<Submission>(sql, [taskId, studentId])
  }

  // 获取最近的提交（用于仪表盘）
  async findRecentSubmissions(limit: number = 10): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      ORDER BY s.submission_date DESC
      LIMIT ?
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [limit])
  }

  // 获取学生最近的提交
  async findRecentByStudentId(studentId: number, limit: number = 5): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE s.student_id = ?
      ORDER BY s.submission_date DESC
      LIMIT ?
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [studentId, limit])
  }

  // 获取教师最近收到的提交
  async findRecentByTeacherId(teacherId: number, limit: number = 10): Promise<SubmissionWithDetails[]> {
    // 确保limit是安全的数字
    const safeLimit = Math.max(1, Math.min(100, Number(limit)))

    const sql = `
      SELECT
        s.*,
        t.title as task_title,
        p.project_id,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE p.teacher_id = ?
      ORDER BY s.submission_date DESC
      LIMIT ${safeLimit}
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [Number(teacherId)])
  }

  // 获取教师的所有提交
  async findByTeacherId(teacherId: number): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT
        s.*,
        t.title as task_title,
        p.project_id,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE p.teacher_id = ?
      ORDER BY s.submission_date DESC
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [teacherId])
  }

  // 根据状态查找提交
  async findByStatus(status: Submission['status']): Promise<SubmissionWithDetails[]> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE s.status = ?
      ORDER BY s.submission_date DESC
    `
    return safeExecuteQuery<SubmissionWithDetails>(sql, [status])
  }

  // 搜索提交
  async searchSubmissions(query: string, teacherId?: number, status?: Submission['status']): Promise<SubmissionWithDetails[]> {
    let sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE (s.progress_description LIKE ? OR t.title LIKE ? OR u.full_name LIKE ?)
    `
    const params = [`%${query}%`, `%${query}%`, `%${query}%`]

    if (teacherId) {
      sql += ' AND p.teacher_id = ?'
      params.push(String(teacherId))
    }

    if (status) {
      sql += ' AND s.status = ?'
      params.push(status)
    }

    sql += ' ORDER BY s.submission_date DESC LIMIT 50'

    return safeExecuteQuery<SubmissionWithDetails>(sql, params)
  }

  // 获取提交统计信息
  async getSubmissionStats(): Promise<SubmissionStats> {
    const sql = `
      SELECT 
        COUNT(*) as total_submissions,
        COUNT(CASE WHEN status = 'submitted' THEN 1 END) as pending_review,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'needs_revision' THEN 1 END) as needs_revision,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress
      FROM submissions
    `
    
    const result = await safeExecuteQuerySingle<SubmissionStats>(sql)
    
    return result || {
      total_submissions: 0,
      pending_review: 0,
      completed: 0,
      needs_revision: 0,
      in_progress: 0
    }
  }

  // 获取教师的提交统计信息
  async getSubmissionStatsByTeacherId(teacherId: number): Promise<SubmissionStats> {
    const sql = `
      SELECT 
        COUNT(*) as total_submissions,
        COUNT(CASE WHEN s.status = 'submitted' THEN 1 END) as pending_review,
        COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN s.status = 'needs_revision' THEN 1 END) as needs_revision,
        COUNT(CASE WHEN s.status = 'in_progress' THEN 1 END) as in_progress
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      WHERE p.teacher_id = ?
    `
    
    const result = await safeExecuteQuerySingle<SubmissionStats>(sql, [teacherId])
    
    return result || {
      total_submissions: 0,
      pending_review: 0,
      completed: 0,
      needs_revision: 0,
      in_progress: 0
    }
  }

  // 检查学生是否已提交任务
  async hasStudentSubmitted(taskId: number, studentId: number): Promise<boolean> {
    const sql = 'SELECT 1 FROM submissions WHERE task_id = ? AND student_id = ? LIMIT 1'
    const result = await safeExecuteQuerySingle(sql, [taskId, studentId])
    return result !== null
  }

  // 获取提交详情（包含完整信息）
  async findByIdWithDetails(submissionId: number): Promise<SubmissionWithDetails | null> {
    const sql = `
      SELECT 
        s.*,
        t.title as task_title,
        p.project_name,
        u.full_name as student_name,
        u.username as student_username,
        p.teacher_id,
        sp.git_repo_url
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON s.student_id = u.user_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
      WHERE s.submission_id = ?
      LIMIT 1
    `
    return safeExecuteQuerySingle<SubmissionWithDetails>(sql, [submissionId])
  }
}
