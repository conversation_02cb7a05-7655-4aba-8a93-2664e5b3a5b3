import { describe, it, expect, beforeEach } from 'vitest'
import { SubmissionRepository, type CreateSubmissionData, type UpdateSubmissionData } from './submission'

describe('SubmissionRepository', () => {
  let repository: SubmissionRepository

  beforeEach(() => {
    repository = new SubmissionRepository()
  })

  describe('repository structure', () => {
    it('should have correct table name and primary key', () => {
      expect(repository['tableName']).toBe('submissions')
      expect(repository['primaryKey']).toBe('submission_id')
    })
  })

  describe('data validation', () => {
    it('should validate submission creation data structure', () => {
      const submissionData: CreateSubmissionData = {
        task_id: 1,
        student_id: 1,
        progress_description: 'Completed the first part of the assignment',
        git_commit_hash: 'abc123def456',
        status: 'submitted'
      }

      expect(submissionData.task_id).toBeDefined()
      expect(submissionData.student_id).toBeDefined()
      expect(submissionData.progress_description).toBeDefined()
      expect(typeof submissionData.task_id).toBe('number')
      expect(typeof submissionData.student_id).toBe('number')
      expect(typeof submissionData.progress_description).toBe('string')
    })

    it('should validate submission update data structure', () => {
      const updateData: UpdateSubmissionData = {
        progress_description: 'Updated progress description',
        git_commit_hash: 'def456ghi789',
        status: 'completed'
      }

      expect(updateData.progress_description).toBeDefined()
      expect(updateData.git_commit_hash).toBeDefined()
      expect(updateData.status).toBeDefined()
    })

    it('should allow optional fields in creation data', () => {
      const submissionData: CreateSubmissionData = {
        task_id: 1,
        student_id: 1,
        progress_description: 'Basic submission'
        // git_commit_hash and status are optional
      }

      expect(submissionData.task_id).toBeDefined()
      expect(submissionData.student_id).toBeDefined()
      expect(submissionData.progress_description).toBeDefined()
      expect(submissionData.git_commit_hash).toBeUndefined()
      expect(submissionData.status).toBeUndefined()
    })
  })

  describe('status validation', () => {
    it('should validate submission status values', () => {
      const validStatuses = ['in_progress', 'submitted', 'completed', 'needs_revision']
      
      validStatuses.forEach(status => {
        expect(validStatuses).toContain(status)
      })
    })

    it('should handle default status in creation', () => {
      const submissionData: CreateSubmissionData = {
        task_id: 1,
        student_id: 1,
        progress_description: 'Test submission'
      }

      const expectedData = {
        task_id: submissionData.task_id,
        student_id: submissionData.student_id,
        progress_description: submissionData.progress_description,
        git_commit_hash: submissionData.git_commit_hash || null,
        status: submissionData.status || 'submitted'
      }

      expect(expectedData.status).toBe('submitted')
      expect(expectedData.git_commit_hash).toBeNull()
    })
  })

  describe('method signatures', () => {
    it('should have all required methods', () => {
      expect(typeof repository.createSubmission).toBe('function')
      expect(typeof repository.updateSubmission).toBe('function')
      expect(typeof repository.updateSubmissionStatus).toBe('function')
      expect(typeof repository.findByTaskId).toBe('function')
      expect(typeof repository.findByStudentId).toBe('function')
      expect(typeof repository.findPendingReviewByTeacherId).toBe('function')
      expect(typeof repository.findLatestByTaskAndStudent).toBe('function')
      expect(typeof repository.findHistoryByTaskAndStudent).toBe('function')
      expect(typeof repository.findRecentSubmissions).toBe('function')
      expect(typeof repository.findRecentByStudentId).toBe('function')
      expect(typeof repository.findRecentByTeacherId).toBe('function')
      expect(typeof repository.findByStatus).toBe('function')
      expect(typeof repository.searchSubmissions).toBe('function')
      expect(typeof repository.getSubmissionStats).toBe('function')
      expect(typeof repository.getSubmissionStatsByTeacherId).toBe('function')
      expect(typeof repository.hasStudentSubmitted).toBe('function')
      expect(typeof repository.findByIdWithDetails).toBe('function')
    })
  })

  describe('interface compliance', () => {
    it('should comply with Submission interface', () => {
      const mockSubmission = {
        submission_id: 1,
        task_id: 1,
        student_id: 1,
        progress_description: 'Test submission',
        git_commit_hash: 'abc123',
        submission_date: new Date(),
        status: 'submitted' as const
      }

      expect(mockSubmission.submission_id).toBeDefined()
      expect(mockSubmission.task_id).toBeDefined()
      expect(mockSubmission.student_id).toBeDefined()
      expect(mockSubmission.progress_description).toBeDefined()
      expect(mockSubmission.submission_date).toBeInstanceOf(Date)
      expect(['in_progress', 'submitted', 'completed', 'needs_revision']).toContain(mockSubmission.status)
    })

    it('should comply with SubmissionWithDetails interface', () => {
      const mockSubmissionWithDetails = {
        submission_id: 1,
        task_id: 1,
        student_id: 1,
        progress_description: 'Test submission',
        git_commit_hash: 'abc123',
        submission_date: new Date(),
        status: 'submitted' as const,
        task_title: 'Test Task',
        project_name: 'Test Project',
        student_name: 'John Doe',
        student_username: 'johndoe',
        teacher_id: 1,
        git_repo_url: 'https://github.com/user/repo.git'
      }

      expect(mockSubmissionWithDetails.task_title).toBeDefined()
      expect(mockSubmissionWithDetails.project_name).toBeDefined()
      expect(mockSubmissionWithDetails.student_name).toBeDefined()
      expect(mockSubmissionWithDetails.student_username).toBeDefined()
      expect(mockSubmissionWithDetails.teacher_id).toBeDefined()
    })

    it('should comply with SubmissionStats interface', () => {
      const mockSubmissionStats = {
        total_submissions: 100,
        pending_review: 15,
        completed: 70,
        needs_revision: 10,
        in_progress: 5
      }

      expect(typeof mockSubmissionStats.total_submissions).toBe('number')
      expect(typeof mockSubmissionStats.pending_review).toBe('number')
      expect(typeof mockSubmissionStats.completed).toBe('number')
      expect(typeof mockSubmissionStats.needs_revision).toBe('number')
      expect(typeof mockSubmissionStats.in_progress).toBe('number')
    })
  })

  describe('query parameter validation', () => {
    it('should validate search parameters', () => {
      const query = 'test submission'
      const teacherId = 1
      const status = 'submitted'

      expect(typeof query).toBe('string')
      expect(typeof teacherId).toBe('number')
      expect(['in_progress', 'submitted', 'completed', 'needs_revision']).toContain(status)
    })

    it('should validate limit parameters', () => {
      const limit = 10

      expect(typeof limit).toBe('number')
      expect(limit).toBeGreaterThan(0)
    })
  })

  describe('data transformation', () => {
    it('should handle null git_commit_hash in creation', () => {
      const submissionData: CreateSubmissionData = {
        task_id: 1,
        student_id: 1,
        progress_description: 'Test submission without git hash'
      }

      const expectedData = {
        task_id: submissionData.task_id,
        student_id: submissionData.student_id,
        progress_description: submissionData.progress_description,
        git_commit_hash: submissionData.git_commit_hash || null,
        status: submissionData.status || 'submitted'
      }

      expect(expectedData.git_commit_hash).toBeNull()
    })

    it('should preserve git_commit_hash when provided', () => {
      const submissionData: CreateSubmissionData = {
        task_id: 1,
        student_id: 1,
        progress_description: 'Test submission with git hash',
        git_commit_hash: 'abc123def456'
      }

      const expectedData = {
        task_id: submissionData.task_id,
        student_id: submissionData.student_id,
        progress_description: submissionData.progress_description,
        git_commit_hash: submissionData.git_commit_hash || null,
        status: submissionData.status || 'submitted'
      }

      expect(expectedData.git_commit_hash).toBe('abc123def456')
    })
  })

  describe('statistics validation', () => {
    it('should validate submission statistics structure', () => {
      const mockStats = {
        total_submissions: 0,
        pending_review: 0,
        completed: 0,
        needs_revision: 0,
        in_progress: 0
      }

      // Check that all counts are non-negative
      Object.values(mockStats).forEach(count => {
        expect(count).toBeGreaterThanOrEqual(0)
      })

      // Check that the sum makes sense (pending + completed + needs_revision + in_progress <= total)
      const activeSubmissions = mockStats.pending_review + mockStats.completed + 
                               mockStats.needs_revision + mockStats.in_progress
      expect(activeSubmissions).toBeLessThanOrEqual(mockStats.total_submissions)
    })
  })
})
