import { describe, it, expect, beforeEach } from 'vitest'
import { TaskRepository, type CreateTaskData, type UpdateTaskData } from './task'

describe('TaskRepository', () => {
  let repository: TaskRepository

  beforeEach(() => {
    repository = new TaskRepository()
  })

  describe('repository structure', () => {
    it('should have correct table name and primary key', () => {
      expect(repository['tableName']).toBe('tasks')
      expect(repository['primaryKey']).toBe('task_id')
    })
  })

  describe('data validation', () => {
    it('should validate task creation data structure', () => {
      const taskData: CreateTaskData = {
        project_id: 1,
        title: 'Test Task',
        description: 'A test task description',
        due_date: new Date('2024-12-31')
      }

      expect(taskData.project_id).toBeDefined()
      expect(taskData.title).toBeDefined()
      expect(typeof taskData.project_id).toBe('number')
      expect(typeof taskData.title).toBe('string')
    })

    it('should validate task update data structure', () => {
      const updateData: UpdateTaskData = {
        title: 'Updated Task Title',
        description: 'Updated description',
        due_date: new Date('2024-12-31')
      }

      expect(updateData.title).toBeDefined()
      expect(updateData.description).toBeDefined()
      expect(updateData.due_date).toBeDefined()
    })

    it('should allow optional fields in creation data', () => {
      const taskData: CreateTaskData = {
        project_id: 1,
        title: 'Test Task'
        // description and due_date are optional
      }

      expect(taskData.project_id).toBeDefined()
      expect(taskData.title).toBeDefined()
      expect(taskData.description).toBeUndefined()
      expect(taskData.due_date).toBeUndefined()
    })

    it('should handle string dates in creation data', () => {
      const taskData: CreateTaskData = {
        project_id: 1,
        title: 'Test Task',
        due_date: '2024-12-31'
      }

      expect(taskData.due_date).toBe('2024-12-31')
      expect(typeof taskData.due_date).toBe('string')
    })
  })

  describe('method signatures', () => {
    it('should have all required methods', () => {
      expect(typeof repository.findByProjectId).toBe('function')
      expect(typeof repository.createTask).toBe('function')
      expect(typeof repository.updateTask).toBe('function')
      expect(typeof repository.findByIdWithProject).toBe('function')
      expect(typeof repository.findByStudentId).toBe('function')
      expect(typeof repository.findByTeacherIdWithStats).toBe('function')
      expect(typeof repository.findUpcomingTasks).toBe('function')
      expect(typeof repository.findInProgressTasks).toBe('function')
      expect(typeof repository.searchTasks).toBe('function')
      expect(typeof repository.getTaskStats).toBe('function')
      expect(typeof repository.isTaskInProject).toBe('function')
      expect(typeof repository.canStudentAccessTask).toBe('function')
    })
  })

  describe('data transformation', () => {
    it('should handle date conversion in task creation', () => {
      const taskData: CreateTaskData = {
        project_id: 1,
        title: 'Test Task',
        description: 'Test Description',
        due_date: '2024-12-31'
      }

      const expectedData = {
        project_id: taskData.project_id,
        title: taskData.title,
        description: taskData.description || null,
        due_date: taskData.due_date ? new Date(taskData.due_date) : null
      }

      expect(expectedData.due_date).toBeInstanceOf(Date)
      expect(expectedData.due_date?.getFullYear()).toBe(2024)
    })

    it('should handle null due_date in task creation', () => {
      const taskData: CreateTaskData = {
        project_id: 1,
        title: 'Test Task'
      }

      const expectedData = {
        project_id: taskData.project_id,
        title: taskData.title,
        description: taskData.description || null,
        due_date: taskData.due_date ? new Date(taskData.due_date) : null
      }

      expect(expectedData.due_date).toBeNull()
      expect(expectedData.description).toBeNull()
    })
  })

  describe('interface compliance', () => {
    it('should comply with Task interface', () => {
      const mockTask = {
        task_id: 1,
        project_id: 1,
        title: 'Test Task',
        description: 'Test Description',
        due_date: new Date(),
        created_at: new Date()
      }

      expect(mockTask.task_id).toBeDefined()
      expect(mockTask.project_id).toBeDefined()
      expect(mockTask.title).toBeDefined()
      expect(mockTask.created_at).toBeInstanceOf(Date)
    })

    it('should comply with TaskWithProject interface', () => {
      const mockTaskWithProject = {
        task_id: 1,
        project_id: 1,
        title: 'Test Task',
        description: 'Test Description',
        due_date: new Date(),
        created_at: new Date(),
        project_name: 'Test Project',
        teacher_id: 1,
        teacher_name: 'John Doe'
      }

      expect(mockTaskWithProject.project_name).toBeDefined()
      expect(mockTaskWithProject.teacher_id).toBeDefined()
      expect(mockTaskWithProject.teacher_name).toBeDefined()
    })

    it('should comply with StudentTaskView interface', () => {
      const mockStudentTaskView = {
        task_id: 1,
        project_id: 1,
        title: 'Test Task',
        description: 'Test Description',
        due_date: new Date(),
        created_at: new Date(),
        project_name: 'Test Project',
        teacher_name: 'John Doe',
        submission_status: 'not_submitted' as const,
        latest_submission_id: null,
        latest_submission_date: null
      }

      expect(mockStudentTaskView.project_name).toBeDefined()
      expect(mockStudentTaskView.teacher_name).toBeDefined()
      expect(mockStudentTaskView.submission_status).toBeDefined()
      expect(['not_submitted', 'in_progress', 'submitted', 'completed', 'needs_revision'])
        .toContain(mockStudentTaskView.submission_status)
    })

    it('should comply with TaskStats interface', () => {
      const mockTaskStats = {
        task_id: 1,
        title: 'Test Task',
        total_students: 10,
        submitted_count: 8,
        completed_count: 5,
        needs_revision_count: 2
      }

      expect(mockTaskStats.task_id).toBeDefined()
      expect(mockTaskStats.title).toBeDefined()
      expect(typeof mockTaskStats.total_students).toBe('number')
      expect(typeof mockTaskStats.submitted_count).toBe('number')
      expect(typeof mockTaskStats.completed_count).toBe('number')
      expect(typeof mockTaskStats.needs_revision_count).toBe('number')
    })
  })

  describe('status validation', () => {
    it('should validate submission status values', () => {
      const validStatuses = ['not_submitted', 'in_progress', 'submitted', 'completed', 'needs_revision']
      
      validStatuses.forEach(status => {
        expect(validStatuses).toContain(status)
      })
    })

    it('should handle task stats calculations', () => {
      const mockStats = {
        total_tasks: 10,
        active_tasks: 7,
        completed_tasks: 3,
        overdue_tasks: 2
      }

      expect(mockStats.total_tasks).toBeGreaterThanOrEqual(mockStats.active_tasks)
      expect(mockStats.total_tasks).toBeGreaterThanOrEqual(mockStats.completed_tasks)
      expect(typeof mockStats.overdue_tasks).toBe('number')
    })
  })

  describe('query parameter validation', () => {
    it('should validate upcoming tasks parameters', () => {
      const studentId = 1
      const days = 7

      expect(typeof studentId).toBe('number')
      expect(typeof days).toBe('number')
      expect(days).toBeGreaterThan(0)
    })

    it('should validate search parameters', () => {
      const query = 'test'
      const projectId = 1
      const teacherId = 1

      expect(typeof query).toBe('string')
      expect(typeof projectId).toBe('number')
      expect(typeof teacherId).toBe('number')
    })
  })
})
