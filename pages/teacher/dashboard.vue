<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          教师仪表盘
        </h1>
        <p class="text-gray-600 mt-2">
          欢迎回来，{{ user?.full_name || user?.username }}
        </p>
      </div>
      <UButton @click="handleLogout" variant="outline">
        登出
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载数据时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">项目统计</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">{{ dashboardData?.summary.total_projects || 0 }}</div>
            <div class="text-sm text-gray-500">活跃项目</div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">学生统计</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ dashboardData?.summary.total_students || 0 }}</div>
            <div class="text-sm text-gray-500">参与学生</div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">待审阅</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-orange-600">{{ dashboardData?.summary.pending_reviews_count || 0 }}</div>
            <div class="text-sm text-gray-500">提交记录</div>
          </div>
        </UCard>
      </div>

      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近提交 -->
        <UCard>
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-semibold">最近提交</h3>
              <UButton
                v-if="dashboardData?.recent_submissions.length"
                variant="ghost"
                size="sm"
                @click="navigateTo('/teacher/reviews')"
              >
                查看全部
              </UButton>
            </div>
          </template>

          <div v-if="!dashboardData?.recent_submissions.length" class="text-center py-8 text-gray-500">
            暂无提交记录
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="submission in dashboardData.recent_submissions.slice(0, 5)"
              :key="submission.submission_id"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              @click="viewSubmission(submission.submission_id)"
            >
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ submission.student_name }}</div>
                <div class="text-sm text-gray-600">{{ submission.task_title }}</div>
                <div class="text-xs text-gray-500">{{ submission.project_name }}</div>
              </div>
              <div class="text-right">
                <UBadge
                  :color="getStatusColor(submission.status)"
                  variant="soft"
                  size="sm"
                >
                  {{ getStatusText(submission.status) }}
                </UBadge>
                <div class="text-xs text-gray-500 mt-1">
                  {{ formatDate(submission.submission_date) }}
                </div>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 快速操作 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">快速操作</h3>
          </template>
          <div class="space-y-3">
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/teacher/projects')"
            >
              <UIcon name="i-heroicons-plus" class="mr-2" />
              创建新项目
            </UButton>
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/teacher/tasks')"
            >
              <UIcon name="i-heroicons-document-plus" class="mr-2" />
              发布任务
            </UButton>
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/teacher/reviews')"
            >
              <UIcon name="i-heroicons-eye" class="mr-2" />
              审阅学生进度
            </UButton>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Auth store
const authStore = useAuthStore()
const user = computed(() => authStore.user)

// 获取仪表盘数据
const { data: dashboardResponse, pending, error, refresh } = await useAuthFetch('/api/teacher/dashboard', {
  server: false,
  default: () => ({
    success: true,
    data: {
      recent_submissions: [],
      pending_reviews: [],
      project_stats: [],
      summary: {
        total_projects: 0,
        total_students: 0,
        pending_reviews_count: 0
      }
    }
  })
})

// 提取实际的数据
const dashboardData = computed(() => dashboardResponse.value?.data || {
  recent_submissions: [],
  pending_reviews: [],
  project_stats: [],
  summary: {
    total_projects: 0,
    total_students: 0,
    pending_reviews_count: 0
  }
})

// Handle logout
const handleLogout = async () => {
  await authStore.logout()
}

// 查看提交详情
const viewSubmission = (submissionId: number) => {
  navigateTo(`/teacher/reviews/${submissionId}`)
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'submitted':
      return 'blue'
    case 'in_progress':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'red'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'submitted':
      return '已提交'
    case 'in_progress':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需修改'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays - 1}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// Set page title
useHead({
  title: '教师仪表盘 - 学生任务管理系统'
})
</script>
