<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">进度审阅</h1>
        <p class="text-gray-600 mt-2">审阅学生提交的任务进度并给出反馈</p>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <UFormGroup label="状态筛选">
        <USelect
          v-model="selectedStatus"
          :options="statusOptions"
        />
      </UFormGroup>

      <UFormGroup label="项目筛选">
        <USelect
          v-model="selectedProjectId"
          :options="projectOptions"
        />
      </UFormGroup>

      <UFormGroup label="搜索">
        <UInput
          v-model="searchQuery"
          placeholder="搜索学生姓名或任务标题"
        />
      </UFormGroup>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载提交列表时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 提交列表 -->
    <div v-else>
      <div v-if="!filteredSubmissions?.length" class="text-center py-12">
        <UIcon name="i-heroicons-document-check" class="text-6xl text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无提交记录</h3>
        <p class="text-gray-500">当前筛选条件下没有找到提交记录</p>
      </div>

      <div v-else class="space-y-4">
        <UCard 
          v-for="submission in filteredSubmissions" 
          :key="submission.submission_id"
          class="hover:shadow-lg transition-shadow cursor-pointer"
          @click="viewSubmission(submission.submission_id)"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-gray-900">
                  {{ submission.task_title }}
                </h3>
                <UBadge 
                  :color="getStatusColor(submission.status)" 
                  variant="soft"
                >
                  {{ getStatusText(submission.status) }}
                </UBadge>
              </div>

              <div class="flex items-center text-sm text-gray-500 mb-2 space-x-4">
                <div class="flex items-center">
                  <UIcon name="i-heroicons-user" class="mr-1" />
                  {{ submission.student_name }}
                </div>
                <div class="flex items-center">
                  <UIcon name="i-heroicons-folder" class="mr-1" />
                  {{ submission.project_name }}
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                {{ submission.progress_description }}
              </p>

              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm">
                  <div v-if="submission.git_commit_hash" class="flex items-center text-blue-600">
                    <UIcon name="i-heroicons-code-bracket" class="mr-1" />
                    {{ submission.git_commit_hash.substring(0, 8) }}
                  </div>
                  <div class="text-gray-500">
                    提交于 {{ formatDate(submission.submission_date) }}
                  </div>
                </div>

                <div class="flex space-x-2">
                  <UButton 
                    v-if="submission.git_repo_url && submission.git_commit_hash"
                    variant="outline" 
                    size="sm"
                    @click.stop="openGitLink(submission)"
                  >
                    <UIcon name="i-heroicons-arrow-top-right-on-square" class="mr-1" />
                    查看代码
                  </UButton>
                  
                  <UButton 
                    v-if="submission.status === 'submitted'"
                    color="primary" 
                    size="sm"
                    @click.stop="reviewSubmission(submission.submission_id)"
                  >
                    <UIcon name="i-heroicons-eye" class="mr-1" />
                    审阅
                  </UButton>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 审阅模态框 -->
    <UModal v-model="showReviewModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">提交反馈</h3>
        </template>

        <form @submit.prevent="submitFeedback" class="space-y-4">
          <UFormGroup label="反馈内容" required>
            <UTextarea 
              v-model="reviewForm.comment" 
              placeholder="请输入您的反馈意见"
              :disabled="submitting"
              rows="4"
            />
          </UFormGroup>

          <UFormGroup label="审阅结果" required>
            <USelect 
              v-model="reviewForm.status" 
              :options="reviewStatusOptions"
              :disabled="submitting"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton 
              variant="outline" 
              @click="showReviewModal = false"
              :disabled="submitting"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="submitting"
            >
              提交反馈
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 类型定义
interface SubmissionData {
  submission_id: number
  task_id: number
  task_title: string
  project_id: number
  project_name: string
  student_id: number
  student_name: string
  student_username: string
  progress_description: string
  git_commit_hash: string | null
  git_repo_url: string | null
  submission_date: string
  status: string
}

interface ProjectData {
  project_id: number
  project_name: string
  description: string | null
  created_at: string
  student_count: number
  task_count: number
}

interface ApiResponse<T> {
  success: boolean
  data: T
}

// 响应式数据
const showReviewModal = ref(false)
const submitting = ref(false)
const selectedStatus = ref('all')
const selectedProjectId = ref<number | string>('all')
const searchQuery = ref('')
const currentSubmissionId = ref<number | null>(null)

// 审阅表单
const reviewForm = ref({
  comment: '',
  status: 'completed' as 'completed' | 'needs_revision'
})

// 获取提交列表
const { data: submissionsResponse, pending, error, refresh } = await useAuthFetch<ApiResponse<SubmissionData[]>>('/api/teacher/reviews', {
  server: false,
  default: () => ({ success: true, data: [] as SubmissionData[] })
})

// 获取项目列表
const { data: projectsResponse } = await useAuthFetch<ApiResponse<ProjectData[]>>('/api/teacher/projects', {
  server: false,
  default: () => ({ success: true, data: [] as ProjectData[] })
})

// 提取实际数据
const submissions = computed(() => (submissionsResponse.value as ApiResponse<SubmissionData[]>)?.data || [])
const projects = computed(() => (projectsResponse.value as ApiResponse<ProjectData[]>)?.data || [])

// 状态选项
const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '待审阅', value: 'submitted' },
  { label: '已完成', value: 'completed' },
  { label: '需修改', value: 'needs_revision' }
]

// 审阅状态选项
const reviewStatusOptions = [
  { label: '已完成', value: 'completed' },
  { label: '需修改', value: 'needs_revision' }
]

// 项目选项
const projectOptions = computed(() => [
  { label: '全部项目', value: 'all' },
  ...(projects.value?.map(project => ({
    label: project.project_name,
    value: project.project_id
  })) || [])
])

// 筛选后的提交
const filteredSubmissions = computed(() => {
  let filtered = submissions.value || []

  // 状态筛选
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(submission => submission.status === selectedStatus.value)
  }

  // 项目筛选
  if (selectedProjectId.value !== 'all') {
    const projectId = Number(selectedProjectId.value)
    filtered = filtered.filter(submission => submission.project_id === projectId)
  }

  // 搜索筛选
  if (searchQuery.value && searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(submission =>
      submission.student_name?.toLowerCase().includes(query) ||
      submission.task_title?.toLowerCase().includes(query)
    )
  }

  return filtered
})



// 查看提交详情
const viewSubmission = (submissionId: number) => {
  navigateTo(`/teacher/submissions/${submissionId}`)
}

// 审阅提交
const reviewSubmission = (submissionId: number) => {
  currentSubmissionId.value = submissionId
  reviewForm.value = {
    comment: '',
    status: 'completed'
  }
  showReviewModal.value = true
}

// 提交反馈
const submitFeedback = async () => {
  if (!currentSubmissionId.value) return

  submitting.value = true
  try {
    const accessToken = useCookie('accessToken')
    await $fetch('/api/teacher/feedback', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      },
      body: {
        submission_id: currentSubmissionId.value,
        comment: reviewForm.value.comment,
        status: reviewForm.value.status
      }
    })

    // 关闭模态框
    showReviewModal.value = false

    // 刷新提交列表
    await refresh()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('提交反馈失败:', error)
    // TODO: 显示错误消息
  } finally {
    submitting.value = false
  }
}

// 打开Git链接
const openGitLink = (submission: any) => {
  if (!submission.git_repo_url || !submission.git_commit_hash) return

  let gitLink = ''
  if (submission.git_repo_url.includes('github.com')) {
    gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
  } else if (submission.git_repo_url.includes('gitlab.com')) {
    gitLink = `${submission.git_repo_url}/-/commit/${submission.git_commit_hash}`
  } else if (submission.git_repo_url.includes('gitee.com')) {
    gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
  } else {
    gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
  }

  window.open(gitLink, '_blank')
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'submitted':
      return 'blue'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'submitted':
      return '待审阅'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需修改'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays - 1}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// Set page title
useHead({
  title: '进度审阅 - 学生任务管理系统'
})
</script>
