<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          学生任务管理系统
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          请登录您的账户
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <UForm 
          :schema="loginSchema" 
          :state="formState" 
          class="space-y-6"
          @submit="handleSubmit"
        >
          <UFormGroup label="用户名" name="username" required>
            <UInput 
              v-model="formState.username"
              placeholder="请输入用户名"
              :disabled="loading"
            />
          </UFormGroup>

          <UFormGroup label="密码" name="password" required>
            <UInput 
              v-model="formState.password"
              type="password"
              placeholder="请输入密码"
              :disabled="loading"
            />
          </UFormGroup>

          <div v-if="error" class="text-red-600 text-sm">
            {{ error }}
          </div>

          <div>
            <UButton 
              type="submit" 
              :loading="loading"
              :disabled="loading"
              class="w-full"
              size="lg"
            >
              {{ loading ? '登录中...' : '登录' }}
            </UButton>
          </div>
        </UForm>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">
                测试账户
              </span>
            </div>
          </div>

          <div class="mt-6 grid grid-cols-2 gap-3">
            <UButton 
              variant="outline" 
              @click="fillTestAccount('teacher')"
              :disabled="loading"
              size="sm"
            >
              教师账户
            </UButton>
            <UButton 
              variant="outline" 
              @click="fillTestAccount('student')"
              :disabled="loading"
              size="sm"
            >
              学生账户
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'

// Set layout
definePageMeta({
  layout: 'auth',
  middleware: []  // Skip auth middleware for login page
})

// Login form schema
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名'),
  password: z.string().min(1, '请输入密码')
})

// Form state
const formState = reactive({
  username: '',
  password: ''
})

// Component state
const loading = ref(false)
const error = ref('')

// Auth store
const authStore = useAuthStore()

// Handle form submission
const handleSubmit = async () => {
  if (loading.value) return
  
  loading.value = true
  error.value = ''

  try {
    const result = await authStore.login({
      username: formState.username,
      password: formState.password
    })

    if (result.success && result.user) {
      // Redirect based on user role
      if (result.user.role === 'teacher') {
        await navigateTo('/teacher/dashboard')
      } else if (result.user.role === 'student') {
        await navigateTo('/student/dashboard')
      } else {
        await navigateTo('/')
      }
    } else {
      error.value = result.error?.message || '登录失败，请检查用户名和密码'
    }
  } catch (err: any) {
    console.error('Login error:', err)
    error.value = err.message || '登录失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// Fill test account credentials
const fillTestAccount = (role: 'teacher' | 'student') => {
  if (role === 'teacher') {
    formState.username = 'teacher1'
    formState.password = 'password123'
  } else {
    formState.username = 'student1'
    formState.password = 'password123'
  }
}

// Redirect if already authenticated
onMounted(async () => {
  const isAuthenticated = authStore.isAuthenticated
  if (isAuthenticated && authStore.user) {
    const user = authStore.user
    if (user.role === 'teacher') {
      await navigateTo('/teacher/dashboard')
    } else if (user.role === 'student') {
      await navigateTo('/student/dashboard')
    }
  }
})
</script>
