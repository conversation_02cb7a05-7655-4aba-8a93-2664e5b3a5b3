import mysql from 'mysql2/promise'

// 数据库配置接口
export interface DatabaseConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
  charset: string
  timezone: string
  connectionLimit: number
}

// 数据库配置
export const dbConfig: DatabaseConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root123',
  database: process.env.DB_NAME || 'projectManageSystem',
  charset: process.env.DB_CHARSET || 'utf8mb4',
  timezone: process.env.DB_TIMEZONE || '+08:00',
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10')
}

// 创建连接池
export const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  queueLimit: 0
})

// 数据库连接测试
export async function testConnection(): Promise<boolean> {
  try {
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()
    console.log('✅ 数据库连接成功')
    return true
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
    return false
  }
}

// 执行查询的工具函数
export async function executeQuery<T = any>(
  sql: string, 
  params: any[] = []
): Promise<T[]> {
  try {
    const [rows] = await pool.execute(sql, params)
    return rows as T[]
  } catch (error) {
    console.error('SQL执行错误:', error)
    throw error
  }
}

// 执行单条查询
export async function executeQuerySingle<T = any>(
  sql: string, 
  params: any[] = []
): Promise<T | null> {
  const results = await executeQuery<T>(sql, params)
  return results.length > 0 ? (results[0] || null) : null
}

// 事务支持
export async function executeTransaction<T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> {
  const connection = await pool.getConnection()
  
  try {
    await connection.beginTransaction()
    const result = await callback(connection)
    await connection.commit()
    return result
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

// 在事务中执行查询
export async function executeQueryInTransaction<T = any>(
  connection: mysql.PoolConnection,
  sql: string,
  params: any[] = []
): Promise<T[]> {
  try {
    const [rows] = await connection.execute(sql, params)
    return rows as T[]
  } catch (error) {
    console.error('事务中SQL执行错误:', error)
    throw error
  }
}

// 在事务中执行单条查询
export async function executeQuerySingleInTransaction<T = any>(
  connection: mysql.PoolConnection,
  sql: string,
  params: any[] = []
): Promise<T | null> {
  const results = await executeQueryInTransaction<T>(connection, sql, params)
  return results.length > 0 ? (results[0] || null) : null
}

export default pool