{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:test": "tsx scripts/init-db.ts", "test": "vitest --run", "test:watch": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@nuxt/ui": "^2.18.7", "@pinia/nuxt": "^0.5.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "nuxt": "^3.13.2", "pinia": "^2.1.7", "vue": "^3.4.38", "vue-router": "^4.4.5", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/mysql": "^2.15.21", "@nuxtjs/tailwindcss": "^6.12.1", "tailwindcss": "^3.4.10", "tsx": "^4.7.0", "vitest": "^1.6.0", "vue-tsc": "^2.1.6"}}