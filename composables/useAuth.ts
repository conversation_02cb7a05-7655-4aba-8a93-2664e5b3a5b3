// Authentication composable

export const useAuth = () => {
  const authStore = useAuthStore()

  return {
    user: computed(() => authStore.user),
    isAuthenticated: computed(() => authStore.isAuthenticated),
    isTeacher: computed(() => authStore.isTeacher),
    isStudent: computed(() => authStore.isStudent),
    login: authStore.login,
    logout: authStore.logout,
    checkAuth: authStore.checkAuth
  }
}