// Authenticated fetch composable
import type { UseFetchOptions } from 'nuxt/app'

export const useAuthFetch = <T>(
  url: string | (() => string),
  options: UseFetchOptions<T> = {}
) => {
  // Get access token from cookie
  const accessToken = useCookie('accessToken')

  // Merge auth headers with existing options
  const authOptions: UseFetchOptions<T> = {
    ...options,
    headers: {
      ...options.headers,
      ...(accessToken.value ? { 'Authorization': `Bearer ${accessToken.value}` } : {})
    },
    onResponseError(context) {
      // Handle 401 errors
      if (context.response.status === 401) {
        const authStore = useAuthStore()
        authStore.logout()
      }

      // Call original onResponseError if provided
      if (options.onResponseError && typeof options.onResponseError === 'function') {
        options.onResponseError(context)
      }
    }
  }

  return useFetch(url, authOptions)
}
