// Login API endpoint
import { UserRepository } from '../../../repositories/user'
import { generateTokenPair } from '../../../utils/jwt'
import { loginSchema } from '../../../utils/validation'
import type { LoginRequest, LoginResponse, ApiError } from '../../../types'

export default defineEventHandler(async (event) => {
  try {
    // Parse request body
    const body = await readBody(event) as LoginRequest

    // Validate input
    const validation = loginSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入数据无效',
          details: validation.error.errors
        }
      } as { success: false; error: ApiError }
    }

    const { username, password } = validation.data

    // Initialize user repository
    const userRepository = new UserRepository()

    // Verify user credentials
    const user = await userRepository.verifyPassword(username, password)
    if (!user) {
      setResponseStatus(event, 401)
      return {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '用户名或密码错误'
        }
      } as { success: false; error: ApiError }
    }

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateTokenPair(user)

    // Set refresh token as httpOnly cookie
    setCookie(event, 'refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    })

    // Return success response with user data and access token
    const response: LoginResponse = {
      success: true,
      user: {
        user_id: user.user_id,
        username: user.username,
        full_name: user.full_name || '',
        role: user.role,
        email: user.email
      },
      token: accessToken
    }

    return response
  } catch (error) {
    console.error('Login error:', error)
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: '登录失败，请稍后重试'
      }
    } as { success: false; error: ApiError }
  }
})
