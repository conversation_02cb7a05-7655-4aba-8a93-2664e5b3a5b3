// Refresh token API endpoint
import { UserRepository } from '../../../repositories/user'
import { verifyRefreshToken, generateTokenPair } from '../../../utils/jwt'
import type { ApiError } from '../../../types'

export default defineEventHandler(async (event) => {
  try {
    // Get refresh token from cookie
    const refreshToken = getCookie(event, 'refreshToken')

    if (!refreshToken) {
      setResponseStatus(event, 401)
      return {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未找到刷新令牌'
        }
      } as { success: false; error: ApiError }
    }

    // Verify refresh token
    const payload = verifyRefreshToken(refreshToken)
    if (!payload) {
      // Clear invalid refresh token cookie
      setCookie(event, 'refreshToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0,
        path: '/'
      })

      setResponseStatus(event, 401)
      return {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的刷新令牌'
        }
      } as { success: false; error: ApiError }
    }

    // Get user from database
    const userRepository = new UserRepository()
    const user = await userRepository.findById(payload.userId)

    if (!user) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '用户不存在'
        }
      } as { success: false; error: ApiError }
    }

    // Generate new token pair
    const { accessToken, refreshToken: newRefreshToken } = generateTokenPair(user)

    // Set new refresh token as httpOnly cookie
    setCookie(event, 'refreshToken', newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    })

    // Return new access token and user data
    return {
      success: true,
      user: {
        user_id: user.user_id,
        username: user.username,
        full_name: user.full_name || '',
        role: user.role,
        email: user.email
      },
      token: accessToken
    }
  } catch (error) {
    console.error('Token refresh error:', error)
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: '令牌刷新失败，请稍后重试'
      }
    } as { success: false; error: ApiError }
  }
})
