// Logout API endpoint
import type { LogoutResponse, ApiError } from '../../../types'

export default defineEventHandler(async (event) => {
  try {
    // Clear the refresh token cookie
    setCookie(event, 'refreshToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/'
    })

    // Return success response
    const response: LogoutResponse = {
      success: true
    }

    return response
  } catch (error) {
    console.error('Logout error:', error)
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: '登出失败，请稍后重试'
      }
    } as { success: false; error: ApiError }
  }
})
