// Get current user API endpoint
import { UserRepository } from '../../../repositories/user'
import { verifyToken, extractTokenFromHeader } from '../../../utils/jwt'
import type { User, ApiError } from '../../../types'

export default defineEventHandler(async (event) => {
  try {
    // Extract token from Authorization header
    const authHeader = getHeader(event, 'authorization')
    const token = extractTokenFromHeader(authHeader)

    if (!token) {
      setResponseStatus(event, 401)
      return {
        success: false,
        user: null,
        error: {
          code: 'UNAUTHORIZED',
          message: '未提供认证令牌'
        }
      } as { success: false; user: null; error: ApiError }
    }

    // Verify token
    const payload = verifyToken(token)
    if (!payload) {
      setResponseStatus(event, 401)
      return {
        success: false,
        user: null,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的认证令牌'
        }
      } as { success: false; user: null; error: ApiError }
    }

    // Get user from database
    const userRepository = new UserRepository()
    const user = await userRepository.findById(payload.userId)

    if (!user) {
      setResponseStatus(event, 404)
      return {
        success: false,
        user: null,
        error: {
          code: 'NOT_FOUND',
          message: '用户不存在'
        }
      } as { success: false; user: null; error: ApiError }
    }

    // Return user data (without password hash)
    return {
      success: true,
      user: {
        user_id: user.user_id,
        username: user.username,
        full_name: user.full_name,
        role: user.role,
        email: user.email,
        created_at: user.created_at
      }
    }
  } catch (error) {
    console.error('Get current user error:', error)
    setResponseStatus(event, 500)
    return {
      success: false,
      user: null,
      error: {
        code: 'DATABASE_ERROR',
        message: '获取用户信息失败，请稍后重试'
      }
    } as { success: false; user: null; error: ApiError }
  }
})
