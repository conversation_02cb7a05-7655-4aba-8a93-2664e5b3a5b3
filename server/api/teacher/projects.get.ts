import { requireTeacher } from '../../../utils/auth-server'
import { ProjectRepository } from '../../../repositories/project'

// 教师项目列表响应接口
interface TeacherProjectsResponse {
  success: boolean
  data: Array<{
    project_id: number
    project_name: string
    description: string | null
    created_at: string
    student_count: number
    task_count: number
  }>
}

export default defineEventHandler(async (event): Promise<TeacherProjectsResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 初始化仓储
    const projectRepository = new ProjectRepository()

    // 获取教师的项目列表（包含统计信息）
    const projects = await projectRepository.findByTeacherIdWithStats(teacher.user_id)

    // 格式化响应数据
    const formattedProjects = projects.map(project => ({
      project_id: project.project_id,
      project_name: project.project_name,
      description: project.description,
      created_at: project.created_at.toISOString(),
      student_count: project.student_count,
      task_count: project.task_count
    }))

    return {
      success: true,
      data: formattedProjects
    }
  } catch (error) {
    console.error('Teacher projects API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
