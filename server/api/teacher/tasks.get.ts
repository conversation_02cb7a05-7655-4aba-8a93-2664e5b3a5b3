import { requireTeacher } from '../../../utils/auth-server'
import { TaskRepository } from '../../../repositories/task'

// 教师任务列表响应接口
interface TeacherTasksResponse {
  success: boolean
  data: Array<{
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
    project_name: string
    total_students: number
    submitted_count: number
    completed_count: number
    needs_revision_count: number
  }>
}

export default defineEventHandler(async (event): Promise<TeacherTasksResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取查询参数
    const query = getQuery(event)
    const projectId = query.project_id ? parseInt(query.project_id as string) : undefined

    // 初始化仓储
    const taskRepository = new TaskRepository()

    // 获取教师的任务列表（包含统计信息）
    const tasks = await taskRepository.findByTeacherIdWithStats(teacher.user_id)

    // 如果指定了项目ID，则过滤任务
    const filteredTasks = projectId 
      ? tasks.filter(task => task.project_id === projectId)
      : tasks

    // 格式化响应数据
    const formattedTasks = filteredTasks.map(task => ({
      task_id: task.task_id,
      title: task.title,
      description: task.description,
      due_date: task.due_date ? task.due_date.toISOString() : null,
      created_at: task.created_at.toISOString(),
      project_id: task.project_id,
      project_name: task.project_name,
      total_students: task.total_students,
      submitted_count: task.submitted_count,
      completed_count: task.completed_count,
      needs_revision_count: task.needs_revision_count
    }))

    return {
      success: true,
      data: formattedTasks
    }
  } catch (error) {
    console.error('Teacher tasks API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
