import { requireTeacher } from '../../../utils/auth-server'
import { FeedbackRepository } from '../../../repositories/feedback'
import { SubmissionRepository } from '../../../repositories/submission'
import { z } from 'zod'

// 创建反馈请求验证
const createFeedbackSchema = z.object({
  submission_id: z.number().min(1, '请选择提交记录'),
  comment: z.string().min(1, '反馈内容不能为空').max(1000, '反馈内容不能超过1000个字符'),
  status: z.enum(['completed', 'needs_revision'], {
    errorMap: () => ({ message: '状态必须是已完成或需修改' })
  })
})

// 创建反馈响应接口
interface CreateFeedbackResponse {
  success: boolean
  data?: {
    feedback_id: number
    submission_id: number
    comment: string
    created_at: string
    status: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<CreateFeedbackResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = createFeedbackSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { submission_id, comment, status } = validation.data

    // 初始化仓储
    const feedbackRepository = new FeedbackRepository()
    const submissionRepository = new SubmissionRepository()

    // 验证提交记录是否存在且属于当前教师
    const submission = await submissionRepository.findByIdWithDetails(submission_id)
    if (!submission) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'SUBMISSION_NOT_FOUND',
          message: '提交记录不存在'
        }
      }
    }

    if (submission.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权对此提交进行反馈'
        }
      }
    }

    // 创建反馈
    const feedback = await feedbackRepository.createFeedback({
      submission_id,
      teacher_id: teacher.user_id,
      comment
    })

    // 更新提交状态
    await submissionRepository.updateSubmissionStatus(submission_id, status)

    return {
      success: true,
      data: {
        feedback_id: feedback.feedback_id,
        submission_id: feedback.submission_id,
        comment: feedback.comment,
        created_at: feedback.created_at.toISOString(),
        status: status
      }
    }
  } catch (error) {
    console.error('Create feedback API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '提交反馈时发生错误'
      }
    }
  }
})
