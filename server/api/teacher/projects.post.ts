import { requireTeacher } from '../../../utils/auth-server'
import { ProjectRepository } from '../../../repositories/project'
import { UserRepository } from '../../../repositories/user'
import { z } from 'zod'

// 创建项目请求验证
const createProjectSchema = z.object({
  project_name: z.string().min(1, '项目名称不能为空').max(100, '项目名称不能超过100个字符'),
  description: z.string().optional(),
  student_ids: z.array(z.number()).optional().default([])
})

// 创建项目响应接口
interface CreateProjectResponse {
  success: boolean
  data?: {
    project_id: number
    project_name: string
    description: string | null
    created_at: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<CreateProjectResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = createProjectSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { project_name, description, student_ids } = validation.data

    // 初始化仓储
    const projectRepository = new ProjectRepository()
    const userRepository = new UserRepository()

    // 验证学生ID是否有效
    if (student_ids.length > 0) {
      for (const studentId of student_ids) {
        const student = await userRepository.findById(studentId)
        if (!student || student.role !== 'student') {
          setResponseStatus(event, 400)
          return {
            success: false,
            error: {
              code: 'INVALID_STUDENT',
              message: `学生ID ${studentId} 无效`
            }
          }
        }
      }
    }

    // 创建项目
    const project = await projectRepository.createProject({
      project_name,
      description,
      teacher_id: teacher.user_id
    })

    // 分配学生到项目
    if (student_ids.length > 0) {
      await projectRepository.assignStudentsToProject(project.project_id, student_ids)
    }

    return {
      success: true,
      data: {
        project_id: project.project_id,
        project_name: project.project_name,
        description: project.description,
        created_at: project.created_at.toISOString()
      }
    }
  } catch (error) {
    console.error('Create project API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '创建项目时发生错误'
      }
    }
  }
})
