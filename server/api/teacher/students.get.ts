import { requireTeacher } from '../../../utils/auth-server'
import { UserRepository } from '../../../repositories/user'

// 学生列表响应接口
interface StudentsResponse {
  success: boolean
  data: Array<{
    user_id: number
    username: string
    full_name: string | null
    email: string
  }>
}

export default defineEventHandler(async (event): Promise<StudentsResponse> => {
  try {
    // 验证教师权限
    await requireTeacher(event)

    // 初始化仓储
    const userRepository = new UserRepository()

    // 获取所有学生
    const students = await userRepository.findByRole('student')

    // 格式化响应数据
    const formattedStudents = students.map(student => ({
      user_id: student.user_id,
      username: student.username,
      full_name: student.full_name,
      email: student.email
    }))

    return {
      success: true,
      data: formattedStudents
    }
  } catch (error) {
    console.error('Students API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
