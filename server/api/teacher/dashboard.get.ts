import { requireTeacher } from '../../../utils/auth-server'
import { ProjectRepository } from '../../../repositories/project'
import { SubmissionRepository } from '../../../repositories/submission'
import { TaskRepository } from '../../../repositories/task'

// 教师仪表盘响应接口
interface TeacherDashboardResponse {
  success: boolean
  data: {
    recent_submissions: Array<{
      submission_id: number
      student_name: string
      task_title: string
      project_name: string
      submission_date: string
      status: string
    }>
    pending_reviews: Array<{
      submission_id: number
      student_name: string
      task_title: string
      project_name: string
      submission_date: string
    }>
    project_stats: Array<{
      project_id: number
      project_name: string
      student_count: number
      task_count: number
    }>
    summary: {
      total_projects: number
      total_students: number
      pending_reviews_count: number
    }
  }
}

export default defineEventHandler(async (event): Promise<TeacherDashboardResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 初始化仓储
    const projectRepository = new ProjectRepository()
    const submissionRepository = new SubmissionRepository()
    const taskRepository = new TaskRepository()

    // 获取教师最近收到的提交（最近10条）
    const recentSubmissions = await submissionRepository.findRecentByTeacherId(teacher.user_id, 10)

    // 获取待审阅的提交
    const pendingReviews = await submissionRepository.findPendingReviewByTeacherId(teacher.user_id)

    // 获取项目统计信息
    const projectStats = await projectRepository.findByTeacherIdWithStats(teacher.user_id)

    // 计算汇总信息
    const totalProjects = projectStats.length
    const totalStudents = projectStats.reduce((sum, project) => sum + project.student_count, 0)
    const pendingReviewsCount = pendingReviews.length

    // 格式化响应数据
    const formattedRecentSubmissions = recentSubmissions.map(submission => ({
      submission_id: submission.submission_id,
      student_name: submission.student_name,
      task_title: submission.task_title,
      project_name: submission.project_name,
      submission_date: submission.submission_date.toISOString(),
      status: submission.status
    }))

    const formattedPendingReviews = pendingReviews.map(submission => ({
      submission_id: submission.submission_id,
      student_name: submission.student_name,
      task_title: submission.task_title,
      project_name: submission.project_name,
      submission_date: submission.submission_date.toISOString()
    }))

    return {
      success: true,
      data: {
        recent_submissions: formattedRecentSubmissions,
        pending_reviews: formattedPendingReviews,
        project_stats: projectStats,
        summary: {
          total_projects: totalProjects,
          total_students: totalStudents,
          pending_reviews_count: pendingReviewsCount
        }
      }
    }
  } catch (error) {
    console.error('Teacher dashboard API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: {
        recent_submissions: [],
        pending_reviews: [],
        project_stats: [],
        summary: {
          total_projects: 0,
          total_students: 0,
          pending_reviews_count: 0
        }
      }
    }
  }
})
