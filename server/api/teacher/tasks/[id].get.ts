import { requireTeacher } from '../../../../utils/auth-server'
import { TaskRepository } from '../../../../repositories/task'
import { ProjectRepository } from '../../../../repositories/project'

// 任务详情响应接口
interface TaskDetailResponse {
  success: boolean
  data?: {
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
    project_name: string
    teacher_id: number
    teacher_name: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<TaskDetailResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取任务ID
    const taskId = parseInt(getRouterParam(event, 'id') || '0')
    if (!taskId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_TASK_ID',
          message: '无效的任务ID'
        }
      }
    }

    // 初始化仓储
    const taskRepository = new TaskRepository()

    // 获取任务详情（包含项目信息）
    const task = await taskRepository.findByIdWithProject(taskId)
    if (!task) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: '任务不存在'
        }
      }
    }

    // 验证任务是否属于当前教师
    if (task.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权访问此任务'
        }
      }
    }

    return {
      success: true,
      data: {
        task_id: task.task_id,
        title: task.title,
        description: task.description,
        due_date: task.due_date ? task.due_date.toISOString() : null,
        created_at: task.created_at.toISOString(),
        project_id: task.project_id,
        project_name: task.project_name,
        teacher_id: task.teacher_id,
        teacher_name: task.teacher_name
      }
    }
  } catch (error) {
    console.error('Task detail API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取任务详情时发生错误'
      }
    }
  }
})
