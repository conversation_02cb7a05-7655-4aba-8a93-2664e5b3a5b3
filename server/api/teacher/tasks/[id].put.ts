import { requireTeacher } from '../../../../utils/auth-server'
import { TaskRepository } from '../../../../repositories/task'
import { z } from 'zod'

// 更新任务请求验证
const updateTaskSchema = z.object({
  title: z.string().min(1, '任务标题不能为空').max(200, '任务标题不能超过200个字符').optional(),
  description: z.string().optional(),
  due_date: z.string().optional().nullable()
})

// 更新任务响应接口
interface UpdateTaskResponse {
  success: boolean
  data?: {
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<UpdateTaskResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取任务ID
    const taskId = parseInt(getRouterParam(event, 'id') || '0')
    if (!taskId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_TASK_ID',
          message: '无效的任务ID'
        }
      }
    }

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = updateTaskSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { title, description, due_date } = validation.data

    // 初始化仓储
    const taskRepository = new TaskRepository()

    // 获取任务详情并验证权限
    const task = await taskRepository.findByIdWithProject(taskId)
    if (!task) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: '任务不存在'
        }
      }
    }

    if (task.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权修改此任务'
        }
      }
    }

    // 更新任务
    const updateData: any = {}
    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (due_date !== undefined) {
      updateData.due_date = due_date ? new Date(due_date) : null
    }

    const updatedTask = await taskRepository.updateTask(taskId, updateData)
    if (!updatedTask) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: '更新任务失败'
        }
      }
    }

    return {
      success: true,
      data: {
        task_id: updatedTask.task_id,
        title: updatedTask.title,
        description: updatedTask.description,
        due_date: updatedTask.due_date ? updatedTask.due_date.toISOString() : null,
        created_at: updatedTask.created_at.toISOString(),
        project_id: updatedTask.project_id
      }
    }
  } catch (error) {
    console.error('Update task API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新任务时发生错误'
      }
    }
  }
})
