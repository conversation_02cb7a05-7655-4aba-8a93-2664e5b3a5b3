import { requireTeacher } from '../../../../utils/auth-server'
import { TaskRepository } from '../../../../repositories/task'

// 删除任务响应接口
interface DeleteTaskResponse {
  success: boolean
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<DeleteTaskResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取任务ID
    const taskId = parseInt(getRouterParam(event, 'id') || '0')
    if (!taskId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_TASK_ID',
          message: '无效的任务ID'
        }
      }
    }

    // 初始化仓储
    const taskRepository = new TaskRepository()

    // 获取任务详情并验证权限
    const task = await taskRepository.findByIdWithProject(taskId)
    if (!task) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: '任务不存在'
        }
      }
    }

    if (task.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权删除此任务'
        }
      }
    }

    // 删除任务
    const deleted = await taskRepository.delete(taskId)
    if (!deleted) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: '删除任务失败'
        }
      }
    }

    return {
      success: true
    }
  } catch (error) {
    console.error('Delete task API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '删除任务时发生错误'
      }
    }
  }
})
