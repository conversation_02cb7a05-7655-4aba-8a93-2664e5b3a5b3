import { requireTeacher } from '../../../utils/auth-server'
import { SubmissionRepository } from '../../../repositories/submission'

// 教师审阅列表响应接口
interface TeacherReviewsResponse {
  success: boolean
  data: Array<{
    submission_id: number
    task_id: number
    task_title: string
    project_id: number
    project_name: string
    student_id: number
    student_name: string
    student_username: string
    progress_description: string
    git_commit_hash: string | null
    git_repo_url: string | null
    submission_date: string
    status: string
  }>
}

export default defineEventHandler(async (event): Promise<TeacherReviewsResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取查询参数
    const query = getQuery(event)
    const status = query.status as string | undefined
    const projectId = query.project_id ? parseInt(query.project_id as string) : undefined
    const taskId = query.task_id ? parseInt(query.task_id as string) : undefined

    // 初始化仓储
    const submissionRepository = new SubmissionRepository()

    let submissions
    
    // 根据状态获取不同的提交列表
    if (status === 'pending') {
      // 获取待审阅的提交
      submissions = await submissionRepository.findPendingReviewByTeacherId(teacher.user_id)
    } else {
      // 获取所有提交
      submissions = await submissionRepository.findByTeacherId(teacher.user_id)
    }

    // 根据项目ID筛选
    if (projectId) {
      submissions = submissions.filter(submission => {
        // 需要通过任务获取项目ID，这里假设submission包含project_id
        return submission.project_id === projectId
      })
    }

    // 根据任务ID筛选
    if (taskId) {
      submissions = submissions.filter(submission => submission.task_id === taskId)
    }

    // 格式化响应数据
    const formattedSubmissions = submissions.map(submission => ({
      submission_id: submission.submission_id,
      task_id: submission.task_id,
      task_title: submission.task_title,
      project_id: submission.project_id || 0, // 需要确保这个字段存在
      project_name: submission.project_name,
      student_id: submission.student_id,
      student_name: submission.student_name,
      student_username: submission.student_username,
      progress_description: submission.progress_description,
      git_commit_hash: submission.git_commit_hash,
      git_repo_url: submission.git_repo_url,
      submission_date: submission.submission_date.toISOString(),
      status: submission.status
    }))

    return {
      success: true,
      data: formattedSubmissions
    }
  } catch (error) {
    console.error('Teacher reviews API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
