import { requireTeacher } from '../../../../utils/auth-server'
import { SubmissionRepository } from '../../../../repositories/submission'
import { FeedbackRepository } from '../../../../repositories/feedback'

// 提交详情响应接口
interface SubmissionDetailResponse {
  success: boolean
  data?: {
    submission_id: number
    task_id: number
    task_title: string
    project_id: number
    project_name: string
    student_id: number
    student_name: string
    student_username: string
    progress_description: string
    git_commit_hash: string | null
    git_repo_url: string | null
    submission_date: string
    status: string
    git_link?: string
    feedback: Array<{
      feedback_id: number
      comment: string
      created_at: string
      teacher_name: string
    }>
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<SubmissionDetailResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取提交ID
    const submissionId = parseInt(getRouterParam(event, 'id') || '0')
    if (!submissionId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_SUBMISSION_ID',
          message: '无效的提交ID'
        }
      }
    }

    // 初始化仓储
    const submissionRepository = new SubmissionRepository()
    const feedbackRepository = new FeedbackRepository()

    // 获取提交详情
    const submission = await submissionRepository.findByIdWithDetails(submissionId)
    if (!submission) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'SUBMISSION_NOT_FOUND',
          message: '提交记录不存在'
        }
      }
    }

    // 验证提交是否属于当前教师
    if (submission.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权访问此提交记录'
        }
      }
    }

    // 获取相关反馈
    const feedback = await feedbackRepository.findBySubmissionId(submissionId)

    // 生成Git链接
    let gitLink: string | undefined
    if (submission.git_repo_url && submission.git_commit_hash) {
      // 根据不同的Git平台生成链接
      if (submission.git_repo_url.includes('github.com')) {
        gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
      } else if (submission.git_repo_url.includes('gitlab.com')) {
        gitLink = `${submission.git_repo_url}/-/commit/${submission.git_commit_hash}`
      } else if (submission.git_repo_url.includes('gitee.com')) {
        gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
      } else {
        // 通用格式
        gitLink = `${submission.git_repo_url}/commit/${submission.git_commit_hash}`
      }
    }

    return {
      success: true,
      data: {
        submission_id: submission.submission_id,
        task_id: submission.task_id,
        task_title: submission.task_title,
        project_id: submission.project_id,
        project_name: submission.project_name,
        student_id: submission.student_id,
        student_name: submission.student_name,
        student_username: submission.student_username,
        progress_description: submission.progress_description,
        git_commit_hash: submission.git_commit_hash,
        git_repo_url: submission.git_repo_url,
        submission_date: submission.submission_date.toISOString(),
        status: submission.status,
        git_link: gitLink,
        feedback: feedback.map(f => ({
          feedback_id: f.feedback_id,
          comment: f.comment,
          created_at: f.created_at.toISOString(),
          teacher_name: f.teacher_name
        }))
      }
    }
  } catch (error) {
    console.error('Submission detail API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取提交详情时发生错误'
      }
    }
  }
})
