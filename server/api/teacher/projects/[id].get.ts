import { requireTeacher } from '../../../../utils/auth-server'
import { ProjectRepository } from '../../../../repositories/project'

// 项目详情响应接口
interface ProjectDetailResponse {
  success: boolean
  data?: {
    project_id: number
    project_name: string
    description: string | null
    created_at: string
    students: Array<{
      user_id: number
      username: string
      full_name: string | null
      email: string
      git_repo_url: string | null
    }>
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<ProjectDetailResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取项目ID
    const projectId = parseInt(getRouterParam(event, 'id') || '0')
    if (!projectId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_PROJECT_ID',
          message: '无效的项目ID'
        }
      }
    }

    // 初始化仓储
    const projectRepository = new ProjectRepository()

    // 获取项目详情
    const project = await projectRepository.findById(projectId)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    // 验证项目是否属于当前教师
    if (project.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权访问此项目'
        }
      }
    }

    // 获取项目的学生列表
    const students = await projectRepository.getProjectStudents(projectId)

    return {
      success: true,
      data: {
        project_id: project.project_id,
        project_name: project.project_name,
        description: project.description,
        created_at: project.created_at.toISOString(),
        students: students
      }
    }
  } catch (error) {
    console.error('Project detail API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取项目详情时发生错误'
      }
    }
  }
})
