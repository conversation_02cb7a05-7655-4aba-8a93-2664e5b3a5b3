import { requireTeacher } from '../../../../utils/auth-server'
import { ProjectRepository } from '../../../../repositories/project'

// 删除项目响应接口
interface DeleteProjectResponse {
  success: boolean
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<DeleteProjectResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取项目ID
    const projectId = parseInt(getRouterParam(event, 'id') || '0')
    if (!projectId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_PROJECT_ID',
          message: '无效的项目ID'
        }
      }
    }

    // 初始化仓储
    const projectRepository = new ProjectRepository()

    // 获取项目详情并验证权限
    const project = await projectRepository.findById(projectId)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    if (project.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权删除此项目'
        }
      }
    }

    // 删除项目
    const deleted = await projectRepository.deleteProject(projectId)
    if (!deleted) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: '删除项目失败'
        }
      }
    }

    return {
      success: true
    }
  } catch (error) {
    console.error('Delete project API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '删除项目时发生错误'
      }
    }
  }
})
