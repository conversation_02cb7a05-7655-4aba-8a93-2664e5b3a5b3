// Core TypeScript interfaces and types for the Student Task Management System

// User related types
export interface User {
  user_id: number
  username: string
  password_hash: string
  email: string
  full_name: string | null
  role: 'teacher' | 'student'
  created_at: Date
}

export interface CreateUserData {
  username: string
  password: string
  email: string
  full_name?: string
  role: 'teacher' | 'student'
}

// Project related types
export interface Project {
  project_id: number
  project_name: string
  description: string | null
  teacher_id: number
  created_at: Date
}

export interface CreateProjectData {
  project_name: string
  description?: string
  teacher_id: number
  student_ids: number[]
}

export interface StudentProject {
  student_id: number
  project_id: number
  git_repo_url: string | null
}

// Task related types
export interface Task {
  task_id: number
  project_id: number
  title: string
  description: string | null
  due_date: Date | null
  created_at: Date
}

export interface CreateTaskData {
  project_id: number
  title: string
  description?: string
  due_date?: Date
}

// Submission related types
export type SubmissionStatus = 'in_progress' | 'submitted' | 'completed' | 'needs_revision'

export interface Submission {
  submission_id: number
  task_id: number
  student_id: number
  progress_description: string
  git_commit_hash: string | null
  submission_date: Date
  status: SubmissionStatus
}

export interface CreateSubmissionData {
  task_id: number
  student_id: number
  progress_description: string
  git_commit_hash?: string
}

// Feedback related types
export interface Feedback {
  feedback_id: number
  submission_id: number
  teacher_id: number
  comment: string
  created_at: Date
}

export interface CreateFeedbackData {
  submission_id: number
  teacher_id: number
  comment: string
}

// API Request/Response types

// Authentication API types
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  success: boolean
  user: {
    user_id: number
    username: string
    full_name: string
    role: 'teacher' | 'student'
    email: string
  }
  token: string
}

export interface LogoutResponse {
  success: boolean
}

export interface MeResponse {
  user: User | null
}

// Student API types
export interface StudentDashboardResponse {
  tasks: {
    in_progress: Task[]
    needs_revision: Task[]
  }
  recent_submissions: Submission[]
  recent_feedback: Feedback[]
  upcoming_deadlines: Task[]
}

export interface StudentProjectsResponse {
  projects: Array<{
    project_id: number
    project_name: string
    description: string
    teacher_name: string
    git_repo_url: string | null
  }>
}

export interface UpdateRepoRequest {
  git_repo_url: string
}

export interface CreateSubmissionRequest {
  task_id: number
  progress_description: string
  git_commit_hash: string
}

// Teacher API types
export interface TeacherDashboardResponse {
  recent_submissions: Array<{
    submission_id: number
    student_name: string
    task_title: string
    submission_date: string
    status: string
  }>
  pending_reviews: Submission[]
  project_stats: Array<{
    project_id: number
    project_name: string
    student_count: number
    task_count: number
  }>
}

export interface CreateProjectRequest {
  project_name: string
  description: string
  student_ids: number[]
}

export interface CreateTaskRequest {
  project_id: number
  title: string
  description: string
  due_date: string
}

export interface CreateFeedbackRequest {
  submission_id: number
  comment: string
  status: 'completed' | 'needs_revision'
}

// Service layer interfaces

export interface UserService {
  authenticate(username: string, password: string): Promise<User | null>
  getUserById(id: number): Promise<User | null>
  updateUser(id: number, data: Partial<User>): Promise<boolean>
  createUser(data: CreateUserData): Promise<User>
}

export interface ProjectService {
  getProjectsByStudent(studentId: number): Promise<Project[]>
  getProjectsByTeacher(teacherId: number): Promise<Project[]>
  createProject(data: CreateProjectData): Promise<Project>
  updateProject(id: number, data: Partial<Project>): Promise<boolean>
  assignStudentsToProject(projectId: number, studentIds: number[]): Promise<boolean>
  deleteProject(id: number): Promise<boolean>
}

export interface TaskService {
  getTasksByProject(projectId: number): Promise<Task[]>
  getTasksByStudent(studentId: number): Promise<Task[]>
  createTask(data: CreateTaskData): Promise<Task>
  updateTask(id: number, data: Partial<Task>): Promise<boolean>
  deleteTask(id: number): Promise<boolean>
}

export interface SubmissionService {
  createSubmission(data: CreateSubmissionData): Promise<Submission>
  getSubmissionsByStudent(studentId: number): Promise<Submission[]>
  getSubmissionsByTask(taskId: number): Promise<Submission[]>
  updateSubmissionStatus(id: number, status: SubmissionStatus): Promise<boolean>
  getSubmissionById(id: number): Promise<Submission | null>
}

export interface FeedbackService {
  createFeedback(data: CreateFeedbackData): Promise<Feedback>
  getFeedbackBySubmission(submissionId: number): Promise<Feedback[]>
  updateFeedback(id: number, data: Partial<Feedback>): Promise<boolean>
}

// State management types

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  token: string | null
}

export interface StudentState {
  projects: Project[]
  tasks: Task[]
  submissions: Submission[]
  currentProject: Project | null
}

export interface TeacherState {
  projects: Project[]
  students: User[]
  pendingReviews: Submission[]
  projectStats: Array<{
    project_id: number
    project_name: string
    student_count: number
    task_count: number
  }>
}

// Error handling types
export interface ApiError {
  code: string
  message: string
  details?: any
}

export enum ErrorCodes {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
}

// Utility types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: ApiError
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}