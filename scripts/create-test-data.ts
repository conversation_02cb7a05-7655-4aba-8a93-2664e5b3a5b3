import { UserRepository } from '../repositories/user'
import { ProjectRepository } from '../repositories/project'
import { TaskRepository } from '../repositories/task'
import { SubmissionRepository } from '../repositories/submission'
import { pool } from '../config/database'

async function createTestData() {
  console.log('🚀 开始创建测试数据...')
  
  try {
    // 初始化仓储
    const userRepository = new UserRepository()
    const projectRepository = new ProjectRepository()
    const taskRepository = new TaskRepository()
    const submissionRepository = new SubmissionRepository()

    // 1. 创建测试用户（如果不存在）
    console.log('👥 检查用户数据...')
    let teacher = await userRepository.findByUsername('teacher1')
    let student = await userRepository.findByUsername('student1')

    if (!teacher) {
      console.log('创建教师用户...')
      teacher = await userRepository.createUser({
        username: 'teacher1',
        password: 'password123',
        email: '<EMAIL>',
        full_name: '张教授',
        role: 'teacher'
      })
    }

    if (!student) {
      console.log('创建学生用户...')
      student = await userRepository.createUser({
        username: 'student1',
        password: 'password123',
        email: '<EMAIL>',
        full_name: '陈小明',
        role: 'student'
      })
    }

    // 2. 创建测试项目
    console.log('📁 创建测试项目...')
    const projects = await projectRepository.findByTeacherId(teacher.user_id)
    let project
    
    if (projects.length === 0) {
      project = await projectRepository.createProject({
        project_name: '软件工程实践',
        description: '学习软件开发的基本流程和方法',
        teacher_id: teacher.user_id,
        student_ids: [student.user_id]
      })
      console.log(`✅ 创建项目: ${project.project_name}`)
    } else {
      project = projects[0]
      console.log(`✅ 使用现有项目: ${project.project_name}`)
    }

    // 3. 创建测试任务
    console.log('📝 创建测试任务...')
    const tasks = await taskRepository.findByProjectId(project.project_id)
    let task
    
    if (tasks.length === 0) {
      task = await taskRepository.createTask({
        project_id: project.project_id,
        title: '第一个任务：环境搭建',
        description: '搭建开发环境，包括IDE、Git等工具的安装和配置',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后
      })
      console.log(`✅ 创建任务: ${task.title}`)
    } else {
      task = tasks[0]
      console.log(`✅ 使用现有任务: ${task.title}`)
    }

    // 4. 创建测试提交
    console.log('📤 创建测试提交...')
    const submissions = await submissionRepository.findByTaskId(task.task_id)
    
    if (submissions.length === 0) {
      const submission = await submissionRepository.createSubmission({
        task_id: task.task_id,
        student_id: student.user_id,
        progress_description: '已完成开发环境的搭建，包括VS Code、Node.js、Git等工具的安装。创建了第一个Hello World项目。',
        git_commit_hash: 'abc123def456',
        status: 'submitted'
      })
      console.log(`✅ 创建提交: ${submission.submission_id}`)
    } else {
      console.log(`✅ 已存在 ${submissions.length} 个提交`)
    }

    console.log('\n🎉 测试数据创建完成！')
    console.log('\n📊 数据统计:')
    console.log(`   - 教师: ${teacher.full_name} (${teacher.username})`)
    console.log(`   - 学生: ${student.full_name} (${student.username})`)
    console.log(`   - 项目: ${project.project_name}`)
    console.log(`   - 任务: ${task.title}`)
    
    const allSubmissions = await submissionRepository.findByTaskId(task.task_id)
    console.log(`   - 提交: ${allSubmissions.length} 个`)

  } catch (error: any) {
    console.error('❌ 创建测试数据失败:', error.message)
    console.error(error)
  } finally {
    // 关闭数据库连接
    await pool.end()
  }
}

// 运行脚本
createTestData().catch(console.error)
