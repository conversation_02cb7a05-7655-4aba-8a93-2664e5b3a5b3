import { testConnection, pool } from '../config/database'

async function initDatabase() {
  console.log('🚀 开始初始化数据库连接...')
  
  const isConnected = await testConnection()
  
  if (isConnected) {
    console.log('✅ 数据库初始化成功！')
    console.log('📊 数据库信息:')
    console.log(`   - 主机: ${process.env.DB_HOST || '127.0.0.1'}`)
    console.log(`   - 端口: ${process.env.DB_PORT || '3306'}`)
    console.log(`   - 数据库: ${process.env.DB_NAME || 'projectManageSystem'}`)
    console.log(`   - 用户: ${process.env.DB_USER || 'root'}`)
  } else {
    console.log('❌ 数据库初始化失败！')
    process.exit(1)
  }
  
  // 关闭连接池
  await pool.end()
}

// 运行初始化
initDatabase().catch(console.error)