import { pool } from '../config/database'

async function checkDatabase() {
  console.log('🔍 检查数据库状态...')
  
  try {
    // 检查表是否存在
    console.log('\n📋 检查表结构...')
    const tables = await pool.execute('SHOW TABLES')
    console.log('现有表:', tables[0])

    // 检查tasks表结构
    console.log('\n📝 检查tasks表结构...')
    try {
      const tasksStructure = await pool.execute('DESCRIBE tasks')
      console.log('tasks表字段:', tasksStructure[0])
    } catch (error) {
      console.log('❌ tasks表不存在或有问题:', error)
    }

    // 检查submissions表结构
    console.log('\n📤 检查submissions表结构...')
    try {
      const submissionsStructure = await pool.execute('DESCRIBE submissions')
      console.log('submissions表字段:', submissionsStructure[0])
    } catch (error) {
      console.log('❌ submissions表不存在或有问题:', error)
    }

    // 检查数据
    console.log('\n📊 检查数据...')
    try {
      const [tasksData] = await pool.execute('SELECT COUNT(*) as count FROM tasks')
      console.log('tasks表数据量:', tasksData)
    } catch (error) {
      console.log('❌ 无法查询tasks表数据:', error)
    }

    try {
      const [submissionsData] = await pool.execute('SELECT COUNT(*) as count FROM submissions')
      console.log('submissions表数据量:', submissionsData)
    } catch (error) {
      console.log('❌ 无法查询submissions表数据:', error)
    }

    // 测试具体的查询
    console.log('\n🧪 测试具体查询...')
    try {
      const testQuery = `
        SELECT 
          s.*,
          t.title as task_title,
          p.project_name,
          u.full_name as student_name,
          u.username as student_username,
          p.teacher_id,
          sp.git_repo_url
        FROM submissions s
        JOIN tasks t ON s.task_id = t.task_id
        JOIN projects p ON t.project_id = p.project_id
        JOIN users u ON s.student_id = u.user_id
        LEFT JOIN student_projects sp ON p.project_id = sp.project_id AND s.student_id = sp.student_id
        WHERE s.submission_id = 1
        LIMIT 1
      `
      const [result] = await pool.execute(testQuery)
      console.log('✅ 查询成功:', result)
    } catch (error) {
      console.log('❌ 查询失败:', error)
    }

  } catch (error: any) {
    console.error('❌ 数据库检查失败:', error.message)
    console.error(error)
  } finally {
    // 关闭数据库连接
    await pool.end()
  }
}

// 运行检查
checkDatabase().catch(console.error)
