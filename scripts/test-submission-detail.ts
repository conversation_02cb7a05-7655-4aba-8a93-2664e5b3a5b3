import { SubmissionRepository } from '../repositories/submission'
import { pool } from '../config/database'

async function testSubmissionDetail() {
  console.log('🧪 测试提交详情查询...')
  
  try {
    const submissionRepository = new SubmissionRepository()
    
    console.log('📤 测试 findByIdWithDetails 方法...')
    const submission = await submissionRepository.findByIdWithDetails(1)
    
    if (submission) {
      console.log('✅ 查询成功:')
      console.log(JSON.stringify(submission, null, 2))
    } else {
      console.log('❌ 未找到提交记录')
    }

  } catch (error: any) {
    console.error('❌ 测试失败:', error.message)
    console.error('SQL错误:', error.sql)
    console.error('参数:', error.params)
    console.error('完整错误:', error)
  } finally {
    // 关闭数据库连接
    await pool.end()
  }
}

// 运行测试
testSubmissionDetail().catch(console.error)
