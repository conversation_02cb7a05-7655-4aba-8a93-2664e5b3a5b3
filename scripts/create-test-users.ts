import { UserRepository, type CreateUserData } from '../repositories/user'
import { pool } from '../config/database'

// 测试用户数据
const testUsers: CreateUserData[] = [
  // 教师账号
  {
    username: 'teacher1',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '张教授',
    role: 'teacher'
  },
  {
    username: 'teacher2',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '李老师',
    role: 'teacher'
  },
  {
    username: 'teacher3',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '王导师',
    role: 'teacher'
  },
  
  // 学生账号
  {
    username: 'student1',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '陈小明',
    role: 'student'
  },
  {
    username: 'student2',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '刘小红',
    role: 'student'
  },
  {
    username: 'student3',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '赵小强',
    role: 'student'
  },
  {
    username: 'student4',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '孙小丽',
    role: 'student'
  },
  {
    username: 'student5',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '周小华',
    role: 'student'
  },
  {
    username: 'student6',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '吴小军',
    role: 'student'
  },
  {
    username: 'student7',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '郑小芳',
    role: 'student'
  },
  {
    username: 'student8',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '马小东',
    role: 'student'
  },
  {
    username: 'student9',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '朱小燕',
    role: 'student'
  },
  {
    username: 'student10',
    password: 'password123',
    email: '<EMAIL>',
    full_name: '许小伟',
    role: 'student'
  }
]

async function createTestUsers() {
  console.log('🚀 开始创建测试用户...')
  
  try {
    const userRepository = new UserRepository()
    
    // 检查是否已有用户
    const existingUsers = await userRepository.findAll()
    if (existingUsers.length > 0) {
      console.log(`⚠️  数据库中已存在 ${existingUsers.length} 个用户`)
      console.log('是否要继续创建测试用户？这可能会导致用户名或邮箱冲突。')
      console.log('如果需要重新创建，请先清空用户表。')
      return
    }
    
    console.log('📝 创建测试用户中...')
    
    // 批量创建用户
    const createdUsers = await userRepository.createUsersInBatch(testUsers)
    
    console.log('✅ 测试用户创建成功！')
    console.log('\n📊 创建的用户列表:')
    console.log('=' .repeat(80))
    
    // 按角色分组显示
    const teachers = createdUsers.filter(user => user.role === 'teacher')
    const students = createdUsers.filter(user => user.role === 'student')
    
    console.log('\n👨‍🏫 教师账号:')
    teachers.forEach(user => {
      console.log(`   用户名: ${user.username.padEnd(12)} | 姓名: ${user.full_name?.padEnd(8)} | 邮箱: ${user.email}`)
    })
    
    console.log('\n👨‍🎓 学生账号:')
    students.forEach(user => {
      console.log(`   用户名: ${user.username.padEnd(12)} | 姓名: ${user.full_name?.padEnd(8)} | 邮箱: ${user.email}`)
    })
    
    console.log('\n🔑 所有账号的默认密码: password123')
    console.log('\n💡 使用说明:')
    console.log('   1. 访问 http://localhost:3000/login')
    console.log('   2. 使用上述任意用户名和密码 "password123" 登录')
    console.log('   3. 教师账号可以创建项目和任务，学生账号可以提交作业')
    
  } catch (error: any) {
    console.error('❌ 创建测试用户失败:', error.message)
    
    if (error.message.includes('已存在')) {
      console.log('\n💡 解决方案:')
      console.log('   1. 检查数据库中是否已有相同用户名或邮箱的用户')
      console.log('   2. 或者修改脚本中的用户名和邮箱')
      console.log('   3. 或者清空用户表后重新运行此脚本')
    }
  } finally {
    // 关闭数据库连接
    await pool.end()
  }
}

// 运行脚本
createTestUsers().catch(console.error)
