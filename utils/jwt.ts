// JWT utility functions for authentication
import jwt from 'jsonwebtoken'
import type { User } from '~/types'

// JWT payload interface
export interface JwtPayload {
  userId: number
  username: string
  role: 'teacher' | 'student'
  iat?: number
  exp?: number
}

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key'
const JWT_EXPIRES_IN = '24h' // Token expires in 24 hours
const JWT_REFRESH_EXPIRES_IN = '7d' // Refresh token expires in 7 days

/**
 * Generate JWT access token
 */
export function generateAccessToken(user: User): string {
  const payload: JwtPayload = {
    userId: user.user_id,
    username: user.username,
    role: user.role
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'student-task-management',
    audience: 'student-task-management-users'
  })
}

/**
 * Generate JWT refresh token
 */
export function generateRefreshToken(user: User): string {
  const payload: JwtPayload = {
    userId: user.user_id,
    username: user.username,
    role: user.role
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_REFRESH_EXPIRES_IN,
    issuer: 'student-task-management',
    audience: 'student-task-management-refresh'
  })
}

/**
 * Verify JWT token
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'student-task-management',
      audience: 'student-task-management-users'
    }) as JwtPayload

    return decoded
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

/**
 * Verify refresh token
 */
export function verifyRefreshToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'student-task-management',
      audience: 'student-task-management-refresh'
    }) as JwtPayload

    return decoded
  } catch (error) {
    console.error('JWT refresh token verification failed:', error)
    return null
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) {
    return null
  }

  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null
  }

  return parts[1] || null
}

/**
 * Get token expiration time
 */
export function getTokenExpiration(token: string): Date | null {
  try {
    const decoded = jwt.decode(token) as JwtPayload
    if (!decoded || !decoded.exp) {
      return null
    }

    return new Date(decoded.exp * 1000)
  } catch (error) {
    console.error('Failed to decode token:', error)
    return null
  }
}

/**
 * Check if token is expired
 */
export function isTokenExpired(token: string): boolean {
  const expiration = getTokenExpiration(token)
  if (!expiration) {
    return true
  }

  return expiration.getTime() < Date.now()
}

/**
 * Generate token pair (access + refresh)
 */
export function generateTokenPair(user: User): { accessToken: string; refreshToken: string } {
  return {
    accessToken: generateAccessToken(user),
    refreshToken: generateRefreshToken(user)
  }
}
