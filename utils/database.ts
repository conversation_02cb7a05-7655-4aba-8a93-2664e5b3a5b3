import { executeQuery, executeQuerySingle, executeTransaction, executeQueryInTransaction } from '../config/database'
import type { PoolConnection } from 'mysql2/promise'

// 数据库错误类型
export enum DatabaseErrorType {
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  FOREIGN_KEY_CONSTRAINT = 'FOREIGN_KEY_CONSTRAINT',
  NOT_FOUND = 'NOT_FOUND'
}

// 数据库错误类
export class DatabaseError extends Error {
  public readonly type: DatabaseErrorType
  public readonly originalError?: Error
  public readonly query?: string
  public readonly params?: any[]

  constructor(
    type: DatabaseErrorType,
    message: string,
    originalError?: Error,
    query?: string,
    params?: any[]
  ) {
    super(message)
    this.name = 'DatabaseError'
    this.type = type
    this.originalError = originalError
    this.query = query
    this.params = params
  }
}

// 错误处理工具函数
export function handleDatabaseError(error: any, query?: string, params?: any[]): DatabaseError {
  if (error instanceof DatabaseError) {
    return error
  }

  // MySQL错误码处理
  if (error.code) {
    switch (error.code) {
      case 'ER_DUP_ENTRY':
        return new DatabaseError(
          DatabaseErrorType.DUPLICATE_ENTRY,
          '记录已存在',
          error,
          query,
          params
        )
      case 'ER_NO_REFERENCED_ROW_2':
      case 'ER_ROW_IS_REFERENCED_2':
        return new DatabaseError(
          DatabaseErrorType.FOREIGN_KEY_CONSTRAINT,
          '外键约束错误',
          error,
          query,
          params
        )
      case 'ECONNREFUSED':
      case 'ENOTFOUND':
        return new DatabaseError(
          DatabaseErrorType.CONNECTION_ERROR,
          '数据库连接失败',
          error,
          query,
          params
        )
      default:
        return new DatabaseError(
          DatabaseErrorType.QUERY_ERROR,
          error.message || '数据库查询错误',
          error,
          query,
          params
        )
    }
  }

  return new DatabaseError(
    DatabaseErrorType.QUERY_ERROR,
    error.message || '未知数据库错误',
    error,
    query,
    params
  )
}

// 安全执行查询的包装函数
export async function safeExecuteQuery<T = any>(
  sql: string,
  params: any[] = []
): Promise<T[]> {
  try {
    return await executeQuery<T>(sql, params)
  } catch (error) {
    throw handleDatabaseError(error, sql, params)
  }
}

// 安全执行单条查询的包装函数
export async function safeExecuteQuerySingle<T = any>(
  sql: string,
  params: any[] = []
): Promise<T | null> {
  try {
    return await executeQuerySingle<T>(sql, params)
  } catch (error) {
    throw handleDatabaseError(error, sql, params)
  }
}

// 安全执行事务的包装函数
export async function safeExecuteTransaction<T>(
  callback: (connection: PoolConnection) => Promise<T>
): Promise<T> {
  try {
    return await executeTransaction(callback)
  } catch (error) {
    throw handleDatabaseError(error)
  }
}

// 数据库工具类
export class DatabaseUtils {

    // 查询多条记录
    static async findMany<T = any>(
        table: string,
        where: Record<string, any> = {},
        orderBy?: string
    ): Promise<T[]> {
        let sql = `SELECT * FROM ${table}`
        const params: any[] = []

        if (Object.keys(where).length > 0) {
            const conditions = Object.keys(where).map(key => {
                params.push(where[key])
                return `${key} = ?`
            })
            sql += ` WHERE ${conditions.join(' AND ')}`
        }

        if (orderBy) {
            sql += ` ORDER BY ${orderBy}`
        }

        return safeExecuteQuery<T>(sql, params)
    }

    // 查询单条记录
    static async findOne<T = any>(
        table: string,
        where: Record<string, any>
    ): Promise<T | null> {
        const conditions = Object.keys(where).map(key => `${key} = ?`)
        const params = Object.values(where)
        const sql = `SELECT * FROM ${table} WHERE ${conditions.join(' AND ')} LIMIT 1`

        return safeExecuteQuerySingle<T>(sql, params)
    }

    // 插入记录
    static async insert(
        table: string,
        data: Record<string, any>
    ): Promise<number> {
        const fields = Object.keys(data)
        const values = Object.values(data)
        const placeholders = fields.map(() => '?').join(', ')

        const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`
        const result = await safeExecuteQuery(sql, values) as any

        return result.insertId
    }

    // 更新记录
    static async update(
        table: string,
        data: Record<string, any>,
        where: Record<string, any>
    ): Promise<number> {
        const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ')
        const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')

        const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
        const params = [...Object.values(data), ...Object.values(where)]

        const result = await safeExecuteQuery(sql, params) as any
        return result.affectedRows
    }

    // 删除记录
    static async delete(
        table: string,
        where: Record<string, any>
    ): Promise<number> {
        const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
        const sql = `DELETE FROM ${table} WHERE ${whereClause}`
        const params = Object.values(where)

        const result = await safeExecuteQuery(sql, params) as any
        return result.affectedRows
    }

    // 统计记录数
    static async count(
        table: string,
        where: Record<string, any> = {}
    ): Promise<number> {
        let sql = `SELECT COUNT(*) as count FROM ${table}`
        const params: any[] = []

        if (Object.keys(where).length > 0) {
            const conditions = Object.keys(where).map(key => {
                params.push(where[key])
                return `${key} = ?`
            })
            sql += ` WHERE ${conditions.join(' AND ')}`
        }

        const result = await safeExecuteQuerySingle<{ count: number }>(sql, params)
        return result?.count || 0
    }

    // 批量插入记录
    static async batchInsert(
        table: string,
        records: Record<string, any>[]
    ): Promise<number[]> {
        if (records.length === 0) return []

        return safeExecuteTransaction(async (connection) => {
            const insertIds: number[] = []
            
            for (const record of records) {
                const fields = Object.keys(record)
                const values = Object.values(record)
                const placeholders = fields.map(() => '?').join(', ')
                
                const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`
                const result = await executeQueryInTransaction(connection, sql, values) as any
                insertIds.push(result.insertId)
            }
            
            return insertIds
        })
    }

    // 批量更新记录
    static async batchUpdate(
        table: string,
        updates: Array<{ data: Record<string, any>, where: Record<string, any> }>
    ): Promise<number> {
        if (updates.length === 0) return 0

        return safeExecuteTransaction(async (connection) => {
            let totalAffected = 0
            
            for (const update of updates) {
                const setClause = Object.keys(update.data).map(key => `${key} = ?`).join(', ')
                const whereClause = Object.keys(update.where).map(key => `${key} = ?`).join(' AND ')
                
                const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
                const params = [...Object.values(update.data), ...Object.values(update.where)]
                
                const result = await executeQueryInTransaction(connection, sql, params) as any
                totalAffected += result.affectedRows
            }
            
            return totalAffected
        })
    }
}