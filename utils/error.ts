// Error handling utilities
import type { ApiError, ErrorCodes } from '~/types'

export class AppError extends Error {
  public code: string
  public statusCode: number
  public details?: any

  constructor(code: string, message: string, statusCode: number = 500, details?: any) {
    super(message)
    this.code = code
    this.statusCode = statusCode
    this.details = details
    this.name = 'AppError'
  }
}

export const createApiError = (
  code: string,
  message: string,
  statusCode: number = 500,
  details?: any
): ApiError => ({
  code,
  message,
  details
})

export const handleApiError = (error: any): ApiError => {
  if (error instanceof AppError) {
    return createApiError(error.code, error.message, error.statusCode, error.details)
  }
  
  // Handle database errors
  if (error.code === 'ER_DUP_ENTRY') {
    return createApiError('DUPLICATE_ENTRY', 'Duplicate entry found', 409)
  }
  
  if (error.code === 'ER_NO_REFERENCED_ROW_2') {
    return createApiError('FOREIGN_KEY_CONSTRAINT', 'Referenced record not found', 400)
  }
  
  // Default error
  return createApiError('INTERNAL_ERROR', 'An internal error occurred', 500)
}