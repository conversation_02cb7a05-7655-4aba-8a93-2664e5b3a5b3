// Validation utilities using Zod
import { z } from 'zod'

// User validation schemas
export const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required')
})

export const createUserSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  email: z.string().email('Invalid email format'),
  full_name: z.string().optional(),
  role: z.enum(['teacher', 'student'])
})

// Project validation schemas
export const createProjectSchema = z.object({
  project_name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  student_ids: z.array(z.number()).min(1, 'At least one student must be assigned')
})

// Task validation schemas
export const createTaskSchema = z.object({
  project_id: z.number(),
  title: z.string().min(1, 'Task title is required'),
  description: z.string().optional(),
  due_date: z.string().optional()
})

// Submission validation schemas
export const createSubmissionSchema = z.object({
  task_id: z.number(),
  progress_description: z.string().min(1, 'Progress description is required'),
  git_commit_hash: z.string().min(1, 'Git commit hash is required')
})

// Feedback validation schemas
export const createFeedbackSchema = z.object({
  submission_id: z.number(),
  comment: z.string().min(1, 'Feedback comment is required'),
  status: z.enum(['completed', 'needs_revision'])
})